"""
高级软件安全保护模块 v2.0
提供反调试、反抓包、反虚拟机、加密等综合安全防护功能

🚀 快速使用：
1. 修改第64行的 SOFTWARE_KEY 为您的软件标识
2. 选择使用方式：
   - UI界面：from advanced_security import show_license_window
   - 控制台：from advanced_security import initialize_security, verify_license

📋 使用示例（UI界面）：
```python
from advanced_security import show_license_window

def main():
    # 显示授权验证窗口
    if show_license_window():
        print("验证成功，启动主程序")
        # 启动您的主程序
    else:
        print("验证失败")
        sys.exit(1)
```

📋 使用示例（控制台）：
```python
from advanced_security import initialize_security, verify_license

def main():
    # 初始化安全保护
    security_manager = initialize_security()

    # 验证许可证
    success, expiry, message = verify_license(auto_load=True)
    if success:
        print(f"验证成功，到期时间: {expiry}")
        # 启动您的主程序
    else:
        print(f"验证失败: {message}")
        sys.exit(1)
```

⚠️ 注意：发布时请将第49行的 DEVELOPMENT_MODE 设为 False
"""

import os
import sys
import time
import ctypes
import base64
import hashlib
import socket
import platform
import psutil
import wmi
import pythoncom
import threading
import random
import string
import urllib.request
import urllib.parse
import ssl
import json
import uuid
import tkinter as tk
from tkinter import ttk, messagebox
import queue
from datetime import datetime
from typing import Optional, Tuple, List, Dict, Any


class SecurityConfig:
    """安全配置类 - 集中管理所有安全参数"""

    # ========================================
    # 🔧 软件标识配置 - 每个软件只需修改这里
    # ========================================
    SOFTWARE_KEY = "2R7W4Y9M2U4T5Z2O"  # ⭐ 修改这里为您的软件标识

    # 开发模式开关（发布时应设为False）
    DEVELOPMENT_MODE = False

    # 编码保护的配置信息（Base64编码隐藏敏感信息）
    _ENCODED_CONFIGS = {
        'api_url': 'aHR0cDovL2FwaS4xd3h5dW4uY29tLz90eXBlPTE3',
        'api_dq': 'aHR0cDovL2FwaTIuMXd4eXVuLmNvbS8/dHlwZT0yNA==',
        'version': 'MS4w',
        'card_file': 'Y2FyZC5kYXQ=',
    }
    
    # 安全策略配置
    SECURITY_POLICIES = {
        'MAX_REQUESTS_PER_HOUR': 60,
        'MIN_REQUEST_INTERVAL': 0.5,
        'ENABLE_ANTI_DEBUG': True,
        'ENABLE_ANTI_VM': True,
        'ENABLE_ANTI_PACKET_CAPTURE': True,
        'ENABLE_FAKE_REQUESTS': True,
        'ENABLE_CONTINUOUS_MONITORING': True,
        'TIMING_CHECK_THRESHOLD': 0.1,  # 时间检测阈值（秒）
        'MONITORING_INTERVAL': 5,  # 监控间隔（秒）
    }
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """获取解密后的配置"""
        return {
            'API_URL': base64.b64decode(cls._ENCODED_CONFIGS['api_url']).decode(),
            'API_DQ': base64.b64decode(cls._ENCODED_CONFIGS['api_dq']).decode(),
            'SOFTWARE_KEY': cls.SOFTWARE_KEY,  # 使用明文软件标识
            'VERSION': base64.b64decode(cls._ENCODED_CONFIGS['version']).decode(),
            'CARD_FILE': base64.b64decode(cls._ENCODED_CONFIGS['card_file']).decode(),
        }
    
    @classmethod
    def is_development_mode(cls) -> bool:
        """检测是否在开发环境中"""
        if cls.DEVELOPMENT_MODE:
            return True
        
        try:
            # 检测IDE环境
            dev_indicators = [
                'pycharm', 'vscode', 'visual studio', 'idle', 'spyder',
                'jupyter', 'anaconda', 'conda'
            ]
            
            # 检查进程名
            try:
                current_process = psutil.Process().name().lower()
                for indicator in dev_indicators:
                    if indicator in current_process:
                        return True
            except:
                pass
            
            # 检查环境变量
            env_vars = ['PYCHARM_HOSTED', 'VSCODE_PID', 'JUPYTER_RUNTIME_DIR']
            for var in env_vars:
                if os.environ.get(var):
                    return True
            
            # 检查文件存在性（开发环境通常有源码文件）
            dev_files = ['main.py', 'requirements.txt', '.git', '.vscode', '.idea']
            dev_file_count = sum(1 for f in dev_files if os.path.exists(f))
            if dev_file_count >= 2:
                return True
                
            return False
        except:
            return False


class CryptoUtils:
    """加密工具类 - 提供各种加密解密功能"""
    
    @staticmethod
    def xor_encrypt(data: str, key: str = "SecureKey2024") -> str:
        """XOR加密（简单但有效的加密方式）"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            key_bytes = key.encode('utf-8')
            result = bytearray()
            for i, byte in enumerate(data):
                result.append(byte ^ key_bytes[i % len(key_bytes)])
            return base64.b64encode(result).decode('utf-8')
        except Exception:
            return data
    
    @staticmethod
    def xor_decrypt(encrypted_data: str, key: str = "SecureKey2024") -> str:
        """XOR解密"""
        try:
            data = base64.b64decode(encrypted_data.encode('utf-8'))
            key_bytes = key.encode('utf-8')
            result = bytearray()
            for i, byte in enumerate(data):
                result.append(byte ^ key_bytes[i % len(key_bytes)])
            return result.decode('utf-8')
        except Exception:
            return encrypted_data
    
    @staticmethod
    def generate_machine_code() -> str:
        """生成机器码 - 基于硬件信息的唯一标识"""
        try:
            # 初始化COM组件（解决多线程WMI调用问题）
            try:
                pythoncom.CoInitialize()
            except:
                pass
            
            c = wmi.WMI()
            hardware_info = []
            
            # 获取CPU信息
            for cpu in c.Win32_Processor():
                if cpu.ProcessorId:
                    hardware_info.append(cpu.ProcessorId.strip())
            
            # 获取主板序列号
            for board in c.Win32_BaseBoard():
                if board.SerialNumber:
                    hardware_info.append(board.SerialNumber.strip())
            
            # 获取BIOS序列号
            for bios in c.Win32_BIOS():
                if bios.SerialNumber:
                    hardware_info.append(bios.SerialNumber.strip())
            
            # 生成机器码
            hardware_info_str = ','.join(filter(None, hardware_info))
            if hardware_info_str:
                hash_object = hashlib.md5(hardware_info_str.encode())
                machine_code = hash_object.hexdigest()
                
                # 清理COM组件
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass
                
                return machine_code
                
        except Exception as e:
            print(f"WMI获取机器码失败: {str(e)}")
            # 清理COM组件
            try:
                pythoncom.CoUninitialize()
            except:
                pass
        
        # 备用方案 - 使用系统信息
        try:
            import uuid
            hardware_info = [
                platform.processor(),
                platform.machine(),
                platform.system() + platform.version(),
                socket.gethostname(),
                str(uuid.getnode())  # MAC地址
            ]
            hardware_info_str = ','.join([str(info) for info in hardware_info if info])
            hash_object = hashlib.md5(hardware_info_str.encode())
            return hash_object.hexdigest()
        except Exception as e:
            print(f"备用方案获取机器码失败: {str(e)}")
            return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()
    
    @staticmethod
    def generate_device_fingerprint() -> str:
        """生成设备指纹 - 更详细的设备标识"""
        try:
            device_info = [
                platform.processor(),
                platform.machine(),
                platform.system(),
                platform.version(),
                socket.gethostname(),
                str(uuid.getnode()),  # MAC地址
                CryptoUtils.generate_machine_code()  # 机器码
            ]
            
            device_str = '|'.join([str(info) for info in device_info if info])
            return hashlib.sha256(device_str.encode()).hexdigest()[:16]
        except:
            return "unknown_device"
    
    @staticmethod
    def generate_request_signature(data_dict: Dict, timestamp: int, nonce: str, secret_key: str) -> str:
        """生成请求签名 - 防止请求被篡改"""
        try:
            # 按键排序并序列化
            sorted_data = json.dumps(data_dict, sort_keys=True, separators=(',', ':'))
            
            # 构建签名字符串
            sign_str = f"{sorted_data}|{timestamp}|{nonce}|{secret_key}"
            
            # 生成签名
            return hashlib.sha256(sign_str.encode()).hexdigest()
        except:
            return ""


class AntiDebugger:
    """反调试检测类 - 检测各种调试环境"""
    
    @staticmethod
    def check_python_debugger() -> bool:
        """检测Python调试器"""
        try:
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
        except:
            pass
        return False
    
    @staticmethod
    def check_windows_debugger() -> bool:
        """检测Windows调试器"""
        try:
            # 检测调试器存在
            if ctypes.windll.kernel32.IsDebuggerPresent():
                return True
            
            # 检测远程调试器
            debug_flag = ctypes.c_bool()
            ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
                ctypes.windll.kernel32.GetCurrentProcess(),
                ctypes.byref(debug_flag)
            )
            if debug_flag.value:
                return True
        except:
            pass
        return False
    
    @staticmethod
    def check_timing_attack() -> bool:
        """时间检测 - 调试时运行会变慢"""
        try:
            start_time = time.perf_counter()
            # 执行一些计算
            _ = sum(i * i for i in range(1000))
            elapsed = time.perf_counter() - start_time
            # 正常情况下应该很快完成
            threshold = SecurityConfig.SECURITY_POLICIES['TIMING_CHECK_THRESHOLD']
            return elapsed > threshold
        except:
            pass
        return False
    
    @staticmethod
    def check_dangerous_processes() -> bool:
        """检测危险进程 - 使用Base64编码隐藏进程名"""
        try:
            # Base64编码的危险进程列表
            encoded_procs = [
                'b2xseWRiZy5leGU=', 'eDY0ZGJnLmV4ZQ==', 'eDMyZGJnLmV4ZQ==', 'd2luZGJnLmV4ZQ==',
                'aWRhLmV4ZQ==', 'aWRhNjQuZXhl', 'Y2hlYXRlbmdpbmUuZXhl', 'cHJvY2Vzc2hhY2tlci5leGU=',
                'aHhkLmV4ZQ==', 'MDEwZWRpdG9yLmV4ZQ==', 'cHJvY21vbi5leGU=', 'cHJvY2V4cC5leGU=',
                'ZG5zcHkuZXhl', 'aWxzcHkuZXhl'
            ]
            
            dangerous_processes = [base64.b64decode(p).decode().lower() for p in encoded_procs]
            
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if proc_name in dangerous_processes:
                        return True
                except:
                    pass
        except:
            pass
        return False
    
    @classmethod
    def comprehensive_check(cls) -> bool:
        """综合反调试检测"""
        if SecurityConfig.is_development_mode():
            return False
        
        if not SecurityConfig.SECURITY_POLICIES['ENABLE_ANTI_DEBUG']:
            return False
        
        checks = [
            cls.check_python_debugger,
            cls.check_windows_debugger,
            cls.check_timing_attack,
            cls.check_dangerous_processes
        ]
        
        for check in checks:
            try:
                if check():
                    return True
            except:
                pass
        
        return False


class AntiPacketCapture:
    """反抓包检测类 - 检测网络监控工具"""
    
    @staticmethod
    def check_proxy_ports() -> bool:
        """检测代理端口"""
        try:
            proxy_ports = [8080, 8888, 3128, 1080, 8081, 8082, 8083, 8090, 9090,
                          9999, 8000, 3000, 5000, 7890, 7891, 1087, 10809, 10808]
            
            for port in proxy_ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(0.05)
                    result = sock.connect_ex(('127.0.0.1', port))
                    sock.close()
                    if result == 0:
                        return True
                except:
                    pass
        except:
            pass
        return False

    @staticmethod
    def check_proxy_environment() -> bool:
        """检测代理环境变量"""
        try:
            proxy_env_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy',
                             'ALL_PROXY', 'all_proxy', 'SOCKS_PROXY', 'socks_proxy']

            for var in proxy_env_vars:
                if os.environ.get(var):
                    return True
        except:
            pass
        return False

    @staticmethod
    def check_capture_tools() -> bool:
        """检测抓包工具进程 - 使用Base64编码隐藏工具名"""
        try:
            # Base64编码的抓包工具列表
            capture_tools_encoded = [
                'd2lyZXNoYXJrLmV4ZQ==', 'ZmlkZGxlci5leGU=', 'Y2hhcmxlcy5leGU=', 'YnVycHN1aXRlLmV4ZQ==',
                'bWl0bXByb3h5LmV4ZQ==', 'cmVxYWJsZS5leGU=', 'cHJveHltYW4uZXhl', 'b3dhc3AtemFwLmV4ZQ==',
                'aHR0cGFuYWx5emVyLmV4ZQ==', 'cGFja2V0Y2FwdHVyZS5leGU=', 'bmV0d29ya21pbmVyLmV4ZQ==',
                'ZXR0ZXJjYXAuZXhl', 'dGNwZHVtcC5leGU=', 'd2luZHVtcC5leGU=', 'aHR0cHdhdGNoLmV4ZQ=='
            ]

            capture_tools = [base64.b64decode(p).decode().lower() for p in capture_tools_encoded]

            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if proc_name in capture_tools:
                        return True
                except:
                    pass
        except:
            pass
        return False

    @classmethod
    def comprehensive_check(cls) -> bool:
        """综合反抓包检测"""
        if SecurityConfig.is_development_mode():
            return False

        if not SecurityConfig.SECURITY_POLICIES['ENABLE_ANTI_PACKET_CAPTURE']:
            return False

        checks = [
            cls.check_proxy_ports,
            cls.check_proxy_environment,
            cls.check_capture_tools
        ]

        for check in checks:
            try:
                if check():
                    return True
            except:
                pass

        return False


class AntiVM:
    """反虚拟机检测类 - 检测虚拟化环境"""

    @staticmethod
    def check_system_info() -> bool:
        """检测系统信息中的虚拟机标识"""
        try:
            vm_indicators = [
                'vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'virtual', 'vm',
                'parallels', 'hyper-v', 'kvm', 'bochs'
            ]

            system_info = platform.platform().lower()
            for indicator in vm_indicators:
                if indicator in system_info:
                    return True
        except:
            pass
        return False

    @staticmethod
    def check_wmi_info() -> bool:
        """通过WMI检测虚拟机"""
        try:
            # 初始化COM组件
            try:
                pythoncom.CoInitialize()
            except:
                pass

            c = wmi.WMI()

            # Base64编码的虚拟机标识
            encoded_vm_indicators = [
                'dm13YXJl', 'dmlydHVhbGJveA==', 'dmJveA==', 'cWVtdQ==',
                'eGVu', 'dmlydHVhbA==', 'dm0=', 'cGFyYWxsZWxz',
                'aHlwZXItdg==', 'a3Zt', 'Ym9jaHM='
            ]
            vm_indicators = [base64.b64decode(vm).decode() for vm in encoded_vm_indicators]

            # 检测BIOS信息
            for bios in c.Win32_BIOS():
                bios_version = bios.Version.lower() if bios.Version else ""
                if any(vm in bios_version for vm in vm_indicators):
                    return True

            # 检测主板信息
            for board in c.Win32_BaseBoard():
                manufacturer = board.Manufacturer.lower() if board.Manufacturer else ""
                product = board.Product.lower() if board.Product else ""
                if any(vm in manufacturer + product for vm in vm_indicators):
                    return True

            # 清理COM组件
            try:
                pythoncom.CoUninitialize()
            except:
                pass

        except:
            # 清理COM组件（即使出错也要清理）
            try:
                pythoncom.CoUninitialize()
            except:
                pass
        return False

    @staticmethod
    def check_registry() -> bool:
        """检测注册表中的虚拟机标识（Windows）"""
        try:
            import winreg
            vm_registry_keys = [
                r"SOFTWARE\VMware, Inc.\VMware Tools",
                r"SOFTWARE\Oracle\VirtualBox Guest Additions",
                r"SYSTEM\ControlSet001\Services\VBoxService"
            ]

            for key_path in vm_registry_keys:
                try:
                    winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    return True
                except:
                    pass
        except:
            pass
        return False

    @classmethod
    def comprehensive_check(cls) -> bool:
        """综合反虚拟机检测"""
        if SecurityConfig.is_development_mode():
            return False

        if not SecurityConfig.SECURITY_POLICIES['ENABLE_ANTI_VM']:
            return False

        checks = [
            cls.check_system_info,
            cls.check_wmi_info,
            cls.check_registry
        ]

        for check in checks:
            try:
                if check():
                    return True
            except:
                pass

        return False


class DeceptionLayer:
    """迷惑层 - 提供假函数和垃圾代码来误导逆向分析"""

    @staticmethod
    def fake_license_check(license_data: str) -> bool:
        """假的许可证检查函数"""
        try:
            # 假的复杂验证逻辑
            fake_hash = hashlib.sha256(f"fake_license_{license_data}_{time.time()}".encode()).hexdigest()
            # 模拟网络延迟
            time.sleep(random.uniform(0.001, 0.01))
            return len(fake_hash) > 32
        except:
            return False

    @staticmethod
    def fake_hardware_validation() -> str:
        """假的硬件验证函数"""
        try:
            fake_hw_data = f"hw_{platform.processor()}_{random.randint(1000, 9999)}"
            return hashlib.sha256(fake_hw_data.encode()).hexdigest()[:16]
        except:
            return "fake_hardware"

    @staticmethod
    def fake_server_communication() -> Dict:
        """假的服务器通信函数"""
        try:
            # 编码的假服务器列表
            encoded_servers = ["bGljZW5zZTEuZmFrZS5jb20=", "bGljZW5zZTIuZmFrZS5jb20="]
            fake_servers = [base64.b64decode(s).decode() for s in encoded_servers]

            fake_response = {
                'status': 'success',
                'token': hashlib.md5(f"fake_token_{time.time()}".encode()).hexdigest()[:16],
                'server': random.choice(fake_servers)
            }

            # 模拟网络延迟
            time.sleep(random.uniform(0.01, 0.05))
            return fake_response
        except:
            return {'status': 'error'}

    @staticmethod
    def fake_encryption(data: str, key: str) -> str:
        """假的高级加密函数"""
        try:
            return base64.b64encode(f"advanced_{data}_{key}_{time.time()}".encode()).decode()
        except:
            return "fake_encrypted"

    @staticmethod
    def execute_deception_routine():
        """执行迷惑例程 - 在后台异步执行"""
        def deception_thread():
            try:
                DeceptionLayer.fake_license_check("fake_license_data")
                DeceptionLayer.fake_hardware_validation()
                DeceptionLayer.fake_server_communication()
                DeceptionLayer.fake_encryption("fake_data", "fake_key")
            except:
                pass

        threading.Thread(target=deception_thread, daemon=True).start()


class SecureNetworkRequest:
    """安全网络请求类 - 提供受保护的网络通信"""

    def __init__(self):
        self.last_request_time = 0
        self.request_count = 0
        self.window_start = time.time()
        self.fake_request_urls = [
            "http://httpbin.org/delay/1",
            "http://httpbin.org/status/200",
            "http://httpbin.org/json",
            "http://www.baidu.com",
            "http://www.google.com"
        ]

    def _check_request_frequency(self) -> Tuple[bool, str]:
        """检查请求频率限制"""
        current_time = time.time()

        # 检查请求间隔
        min_interval = SecurityConfig.SECURITY_POLICIES['MIN_REQUEST_INTERVAL']
        if current_time - self.last_request_time < min_interval:
            return False, "请求过于频繁"

        # 检查请求窗口
        if current_time - self.window_start > 3600:  # 重置窗口
            self.request_count = 0
            self.window_start = current_time

        max_requests = SecurityConfig.SECURITY_POLICIES['MAX_REQUESTS_PER_HOUR']
        if self.request_count >= max_requests:
            return False, "超过每小时请求限制"

        return True, "验证通过"

    def _send_fake_requests(self):
        """发送假请求混淆抓包分析"""
        if not SecurityConfig.SECURITY_POLICIES['ENABLE_FAKE_REQUESTS']:
            return

        def fake_request_thread():
            try:
                fake_url = random.choice(self.fake_request_urls)
                fake_data = {
                    'fake_param': random.randint(1000, 9999),
                    'timestamp': int(time.time()),
                    'random_data': ''.join(random.choices(string.ascii_letters, k=10))
                }
                # 不实际发送，只是模拟
                time.sleep(random.uniform(0.1, 0.5))
            except:
                pass

        # 启动多个假请求线程
        for _ in range(random.randint(2, 5)):
            threading.Thread(target=fake_request_thread, daemon=True).start()

    def secure_request(self, url: str, data: Any, headers: Optional[Dict] = None) -> Tuple[Optional[str], str]:
        """安全的网络请求"""
        # 检测抓包工具
        if AntiPacketCapture.comprehensive_check():
            print("检测到网络监控工具，程序退出")
            os._exit(1)

        # 检查请求频率
        can_request, message = self._check_request_frequency()
        if not can_request:
            return None, message

        # 发送假请求混淆
        self._send_fake_requests()

        try:
            # 更新请求统计
            self.last_request_time = time.time()
            self.request_count += 1

            # 准备请求数据
            if headers is None:
                headers = {}

            # 添加安全头
            headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Content-Type': 'application/x-www-form-urlencoded'
            })

            # 准备请求数据
            if data:
                post_data = data.encode('utf-8') if isinstance(data, str) else data
            else:
                post_data = None

            # 创建请求
            req = urllib.request.Request(url, data=post_data, headers=headers)

            # 创建SSL上下文
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # 发送请求
            with urllib.request.urlopen(req, timeout=10, context=ssl_context) as response:
                response_data = response.read().decode('utf-8')
                return response_data, "请求成功"

        except Exception as e:
            return None, f"网络请求失败: {str(e)}"


class SecurityManager:
    """安全管理器 - 统一管理所有安全检测"""

    def __init__(self):
        self.monitoring_active = False
        self.monitoring_thread = None

    def startup_security_check(self):
        """程序启动时的安全检测"""
        if SecurityConfig.is_development_mode():
            print("开发模式：跳过启动安全检测")
            return

        print("正在进行安全环境检测...")

        # 立即检测调试环境
        if AntiDebugger.comprehensive_check():
            print("检测到调试环境，程序退出")
            os._exit(1)

        # 立即检测虚拟机环境
        if AntiVM.comprehensive_check():
            print("检测到虚拟机环境，程序退出")
            os._exit(1)

        # 立即检测抓包工具
        if AntiPacketCapture.comprehensive_check():
            print("检测到网络监控工具，程序退出")
            os._exit(1)

        print("安全检测通过")

        # 执行迷惑例程
        DeceptionLayer.execute_deception_routine()

    def start_continuous_monitoring(self):
        """启动持续安全监控"""
        if not SecurityConfig.SECURITY_POLICIES['ENABLE_CONTINUOUS_MONITORING']:
            return

        if SecurityConfig.is_development_mode():
            print("开发模式：跳过持续监控")
            return

        if self.monitoring_active:
            return

        self.monitoring_active = True

        def monitoring_thread():
            interval = SecurityConfig.SECURITY_POLICIES['MONITORING_INTERVAL']

            while self.monitoring_active:
                time.sleep(interval)

                try:
                    # 检测调试环境
                    if AntiDebugger.comprehensive_check():
                        os._exit(1)

                    # 检测虚拟机环境
                    if AntiVM.comprehensive_check():
                        os._exit(1)

                    # 检测抓包工具
                    if AntiPacketCapture.comprehensive_check():
                        os._exit(1)

                    # 执行迷惑例程
                    DeceptionLayer.execute_deception_routine()

                except:
                    pass

        self.monitoring_thread = threading.Thread(target=monitoring_thread, daemon=True)
        self.monitoring_thread.start()

    def stop_monitoring(self):
        """停止安全监控"""
        self.monitoring_active = False


class LicenseManager:
    """许可证管理器 - 处理授权验证"""

    def __init__(self):
        self.config = SecurityConfig.get_config()
        self.crypto = CryptoUtils()
        self.network = SecureNetworkRequest()

    def save_license(self, license_key: str) -> bool:
        """保存许可证到本地文件（加密存储为dat格式）"""
        try:
            # 加密许可证
            encrypted_license = self.crypto.xor_encrypt(license_key)

            # 添加时间戳和机器码验证
            machine_code = self.crypto.generate_machine_code()
            timestamp = int(time.time())

            # 构建保存数据
            save_data = {
                'license': encrypted_license,
                'machine': machine_code,
                'timestamp': timestamp,
                'version': self.config['VERSION']
            }

            # 将数据转换为JSON并加密
            json_data = json.dumps(save_data)
            final_encrypted = self.crypto.xor_encrypt(json_data, "LicenseKey2024")

            # 保存为dat文件
            with open(self.config['CARD_FILE'], 'wb') as f:
                f.write(base64.b64encode(final_encrypted.encode('utf-8')))

            # 静默保存，不显示提示信息
            return True
        except Exception as e:
            print(f"❌ 保存许可证失败: {str(e)}")
            return False

    def load_license(self) -> str:
        """从本地文件加载许可证（从dat格式）"""
        try:
            if os.path.exists(self.config['CARD_FILE']):
                with open(self.config['CARD_FILE'], 'rb') as f:
                    encrypted_data = f.read()

                if encrypted_data:
                    # 解码Base64
                    decoded_data = base64.b64decode(encrypted_data).decode('utf-8')

                    # 解密数据
                    decrypted_json = self.crypto.xor_decrypt(decoded_data, "LicenseKey2024")

                    # 解析JSON
                    save_data = json.loads(decrypted_json)

                    # 验证机器码
                    current_machine = self.crypto.generate_machine_code()
                    if save_data.get('machine') != current_machine:
                        print("⚠️ 机器码不匹配，许可证可能不适用于当前设备")
                        return ""

                    # 解密许可证
                    license_key = self.crypto.xor_decrypt(save_data.get('license', ''))

                    if license_key:
                        # 静默加载，不显示提示信息
                        return license_key

            return ""
        except Exception as e:
            print(f"❌ 加载许可证失败: {str(e)}")
            return ""

    def verify_license(self, license_key: str, fast_mode: bool = True) -> Tuple[bool, Optional[str], str]:
        """验证许可证"""
        try:
            # 快速安全检测
            if not fast_mode:
                if AntiDebugger.comprehensive_check():
                    print("检测到调试环境，程序退出")
                    os._exit(1)

                if AntiPacketCapture.comprehensive_check():
                    print("检测到网络监控工具，程序退出")
                    os._exit(1)

            # 执行迷惑例程
            DeceptionLayer.execute_deception_routine()

            # 获取机器码
            machine_code = self.crypto.generate_machine_code()
            if not machine_code:
                return False, None, "无法获取机器码"

            # 准备验证数据
            verify_data = f"Softid={self.config['SOFTWARE_KEY']}&Card={license_key}&Version={self.config['VERSION']}&Mac={machine_code}"

            # 发送验证请求
            response_data, message = self.network.secure_request(self.config['API_URL'], verify_data)

            if response_data is None:
                return False, None, f"网络请求失败: {message}"

            # 解析响应
            try:
                # 检查是否为JSON格式
                try:
                    response_json = json.loads(response_data)
                    if isinstance(response_json, int):
                        error_codes = {
                            -81004: "卡密无效或已过期",
                            -81001: "参数错误",
                            -81002: "服务器内部错误",
                            -81003: "网络连接失败",
                            404: "服务未找到",
                            500: "服务器错误"
                        }
                        error_msg = error_codes.get(response_json, f"未知错误代码: {response_json}")
                        return False, None, f"验证失败: {error_msg}"
                    elif isinstance(response_json, dict):
                        if response_json.get('code') == 1:
                            token = response_json.get('token', '')
                        else:
                            error_msg = response_json.get('msg', '验证失败')
                            return False, None, error_msg
                    else:
                        return False, None, f"服务器响应格式错误"
                except json.JSONDecodeError:
                    # 纯文本Token
                    token = response_data.strip()
                    if len(token) != 16 or not token.isalnum():
                        return False, None, f"验证失败: {token}"

                # 获取到期时间
                expiry_data = f"Softid={self.config['SOFTWARE_KEY']}&UserName={license_key}&UserPwd="
                expiry_response, expiry_message = self.network.secure_request(self.config['API_DQ'], expiry_data)

                if expiry_response:
                    try:
                        expiry_json = json.loads(expiry_response)
                        if isinstance(expiry_json, dict) and expiry_json.get('code') == 1:
                            expiry_status = expiry_json.get('endtime', '未知')
                        else:
                            return False, None, f"获取到期时间失败: {expiry_json.get('msg', '未知错误')}"
                    except json.JSONDecodeError:
                        expiry_status = expiry_response.strip()

                    # 验证日期格式
                    try:
                        datetime.strptime(expiry_status, "%Y-%m-%d %H:%M:%S")

                        # 执行成功后的迷惑例程
                        DeceptionLayer.execute_deception_routine()

                        return True, expiry_status, f"验证成功！到期时间: {expiry_status}"
                    except ValueError:
                        return False, None, f"到期时间格式错误: {expiry_status}"
                else:
                    return False, None, f"获取到期时间失败: {expiry_message}"

            except Exception as e:
                return False, None, f"响应解析失败: {str(e)}"

        except Exception as e:
            return False, None, f"验证过程中发生错误: {str(e)}"


# === 主要API函数 ===

def initialize_security() -> SecurityManager:
    """初始化安全保护系统"""
    security_manager = SecurityManager()
    security_manager.startup_security_check()
    security_manager.start_continuous_monitoring()
    return security_manager


def verify_license(license_key: Optional[str] = None, auto_load: bool = False, fast_mode: bool = True) -> Tuple[bool, Optional[str], str]:
    """验证许可证的主要API函数"""
    license_manager = LicenseManager()

    if license_key is None and auto_load:
        license_key = license_manager.load_license()

    if not license_key:
        return False, None, "未提供许可证，请输入有效的许可证"

    success, expiry, message = license_manager.verify_license(license_key, fast_mode)

    if success:
        license_manager.save_license(license_key)

    return success, expiry, message


def get_machine_code() -> str:
    """获取机器码"""
    return CryptoUtils.generate_machine_code()


def save_license_file(license_key: str) -> bool:
    """保存许可证到文件"""
    license_manager = LicenseManager()
    return license_manager.save_license(license_key)


def load_license_file() -> str:
    """从文件加载许可证"""
    license_manager = LicenseManager()
    return license_manager.load_license()


class LicenseWindow:
    """授权验证UI界面"""

    def __init__(self, root):
        self.root = root
        self.result_queue = queue.Queue()
        self.is_verified = False
        self.license_manager = LicenseManager()

        self.root.title("软件授权验证")
        self.setup_window()
        self.create_ui()
        self.check_saved_license()
        self.process_queue()

    def setup_window(self):
        """设置窗口属性"""
        self.root.geometry("420x280")
        self.root.resizable(False, False)

        # 窗口居中
        window_width, window_height = 420, 280
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        center_x = int(screen_width / 2 - window_width / 2)
        center_y = int(screen_height / 2 - window_height / 2)
        self.root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')

    def create_ui(self):
        """创建用户界面"""
        # 执行安全检测
        print("正在进行安全环境检测...")

        # 获取机器码
        machine_code = get_machine_code()
        self.license_key_var = tk.StringVar()

        # 主框架
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(expand=True, fill="both")

        # 标题
        ttk.Label(main_frame, text="软件授权验证",
                 font=("Microsoft YaHei UI", 16, "bold")).pack(pady=(0, 20))

        # 内容框架
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill="x", padx=10)

        # 机器码显示
        ttk.Label(content_frame, text="机器码:").grid(row=0, column=0, sticky="e", padx=5, pady=8)
        ttk.Entry(content_frame, textvariable=tk.StringVar(value=machine_code),
                 state="readonly", width=30).grid(row=0, column=1, sticky="ew", padx=5, pady=8)
        ttk.Button(content_frame, text="复制",
                  command=lambda: [self.root.clipboard_clear(), self.root.clipboard_append(machine_code)],
                  width=6).grid(row=0, column=2, padx=(5, 0), pady=8)

        # 卡密输入
        ttk.Label(content_frame, text="卡密:").grid(row=1, column=0, sticky="e", padx=5, pady=8)
        license_entry = ttk.Entry(content_frame, textvariable=self.license_key_var, width=30)
        license_entry.grid(row=1, column=1, sticky="ew", padx=5, pady=8)
        license_entry.focus()

        # 绑定回车键
        license_entry.bind('<Return>', lambda e: self.on_verify_click())

        content_frame.columnconfigure(1, weight=1)

        # 按钮框架
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        self.verify_btn = ttk.Button(buttons_frame, text="验证授权", command=self.on_verify_click)
        self.verify_btn.pack(side="left", padx=15)
        ttk.Button(buttons_frame, text="退出", command=self.root.destroy).pack(side="left", padx=15)

        # 底部信息
        ttk.Label(main_frame, text="如需购买请联系作者 qq:597882859",
                 foreground="gray").pack(side="bottom", pady=5)

    def check_saved_license(self):
        """检查保存的卡密"""
        saved_license = self.license_manager.load_license()
        if saved_license:
            print("📄 找到保存的授权信息，正在自动验证...")
            self.license_key_var.set(saved_license)
            # 自动验证保存的卡密，提供流畅的用户体验
            self.root.after(500, self.on_verify_click)  # 稍微延迟让用户看到界面
        else:
            print("📄 未找到保存的授权信息，请手动输入卡密")

    def on_verify_click(self):
        """验证按钮点击事件"""
        license_key = self.license_key_var.get().strip()
        if not license_key:
            messagebox.showwarning("提示", "卡密不能为空")
            return

        self.verify_btn.config(state="disabled", text="验证中...")
        threading.Thread(target=self._verify_in_thread, args=(license_key,), daemon=True).start()

    def _verify_in_thread(self, license_key):
        """在线程中执行验证"""
        try:
            # 初始化COM组件（用于WMI调用）
            try:
                pythoncom.CoInitializeEx(0)
            except:
                pass

            success, expiry, message = self.license_manager.verify_license(license_key)

            # 如果验证成功，静默保存许可证到dat文件
            if success:
                try:
                    self.license_manager.save_license(license_key)
                    # 静默保存，不显示提示信息
                except Exception:
                    # 保存失败也不显示错误，避免干扰用户体验
                    pass

            self.result_queue.put((success, expiry, message))

        except Exception as e:
            self.result_queue.put((False, None, f"验证线程错误: {e}", None))
        finally:
            # 清理COM组件
            try:
                pythoncom.CoUninitialize()
            except:
                pass

    def process_queue(self):
        """处理验证结果队列"""
        try:
            success, expiry, message = self.result_queue.get(block=False)

            if success:
                self.is_verified = True
                # 只显示验证成功和到期时间，不显示保存提示
                success_msg = f"授权验证成功！\n到期时间: {expiry}"
                messagebox.showinfo("验证成功", success_msg)
                self.root.destroy()
            else:
                messagebox.showerror("验证失败", message)
                self.verify_btn.config(state="normal", text="验证授权")

        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.process_queue)


def show_license_window() -> bool:
    """显示授权验证窗口"""
    try:
        # 启动安全监控
        security_manager = SecurityManager()
        security_manager.startup_security_check()
        security_manager.start_continuous_monitoring()

        # 创建授权窗口
        auth_root = tk.Tk()
        license_app = LicenseWindow(auth_root)
        auth_root.mainloop()

        return license_app.is_verified

    except Exception as e:
        print(f"显示授权窗口失败: {str(e)}")
        return False


def quick_verify_with_ui() -> Tuple[bool, Optional[str], str]:
    """快速验证（带UI界面）"""
    if show_license_window():
        # 验证成功，获取许可证信息
        license_manager = LicenseManager()
        saved_license = license_manager.load_license()
        if saved_license:
            return license_manager.verify_license(saved_license)

    return False, None, "用户取消验证或验证失败"


# === 导出的主要类和函数 ===
__all__ = [
    'SecurityConfig',
    'CryptoUtils',
    'AntiDebugger',
    'AntiPacketCapture',
    'AntiVM',
    'DeceptionLayer',
    'SecureNetworkRequest',
    'SecurityManager',
    'LicenseManager',
    'LicenseWindow',
    'initialize_security',
    'verify_license',
    'get_machine_code',
    'save_license_file',
    'load_license_file',
    'show_license_window',
    'quick_verify_with_ui'
]


# === 使用示例和文档 ===

"""
使用示例：

1. 基本使用（在您的主程序中）：
```python
from advanced_security import initialize_security, verify_license

# 初始化安全保护
security_manager = initialize_security()

# 验证许可证
success, expiry, message = verify_license("your_license_key")
if success:
    print(f"验证成功，到期时间: {expiry}")
    # 在这里启动您的主程序
else:
    print(f"验证失败: {message}")
    sys.exit(1)
```

2. 高级使用（自定义配置）：
```python
from advanced_security import SecurityConfig, LicenseManager, get_machine_code

# 修改安全配置
SecurityConfig.SECURITY_POLICIES['ENABLE_ANTI_DEBUG'] = True
SecurityConfig.SECURITY_POLICIES['MONITORING_INTERVAL'] = 10

# 获取机器码
machine_code = get_machine_code()
print(f"当前机器码: {machine_code}")

# 创建许可证管理器
license_manager = LicenseManager()

# 验证指定许可证
success, expiry, message = license_manager.verify_license("your_license_key")
if success:
    print(f"验证成功，到期时间: {expiry}")
else:
    print(f"验证失败: {message}")
```

3. 网络请求保护：
```python
from advanced_security import SecureNetworkRequest

# 创建安全网络请求实例
secure_net = SecureNetworkRequest()

# 发送安全请求
response, message = secure_net.secure_request("http://api.example.com", "key=value")
if response:
    print("请求成功")
else:
    print(f"请求失败: {message}")
```

4. 单独使用安全检测：
```python
from advanced_security import AntiDebugger, AntiVM, AntiPacketCapture

# 检测调试环境
if AntiDebugger.comprehensive_check():
    print("检测到调试环境")
    sys.exit(1)

# 检测虚拟机环境
if AntiVM.comprehensive_check():
    print("检测到虚拟机环境")
    sys.exit(1)

# 检测抓包工具
if AntiPacketCapture.comprehensive_check():
    print("检测到网络监控工具")
    sys.exit(1)
```

安全特性说明：
- ✅ 多层反调试检测（Python调试器、Windows调试器、进程检测、时间检测）
- ✅ 反虚拟机检测（系统信息、WMI、注册表）
- ✅ 反抓包检测（代理端口、环境变量、抓包工具进程）
- ✅ 安全网络请求（频率限制、假请求混淆、SSL保护）
- ✅ 硬件绑定（WMI机器码生成、设备指纹）
- ✅ 加密保护（XOR加密、Base64编码、请求签名）
- ✅ 迷惑技术（假函数、垃圾代码、异步迷惑例程）
- ✅ 持续监控（实时安全检测线程）
- ✅ 开发环境友好（自动检测开发环境并跳过检测）
- ✅ 配置化管理（集中配置所有安全策略）

配置说明：
- DEVELOPMENT_MODE: 开发模式开关，发布时设为False
- SECURITY_POLICIES: 安全策略配置字典
  - ENABLE_ANTI_DEBUG: 启用反调试检测
  - ENABLE_ANTI_VM: 启用反虚拟机检测
  - ENABLE_ANTI_PACKET_CAPTURE: 启用反抓包检测
  - ENABLE_FAKE_REQUESTS: 启用假请求混淆
  - ENABLE_CONTINUOUS_MONITORING: 启用持续监控
  - MAX_REQUESTS_PER_HOUR: 每小时最大请求数
  - MIN_REQUEST_INTERVAL: 最小请求间隔（秒）
  - TIMING_CHECK_THRESHOLD: 时间检测阈值（秒）
  - MONITORING_INTERVAL: 监控间隔（秒）

注意事项：
1. 发布时请将 SecurityConfig.DEVELOPMENT_MODE 设置为 False
2. 根据需要修改 SecurityConfig._ENCODED_CONFIGS 中的服务器地址和密钥
3. 可以通过 SecurityConfig.SECURITY_POLICIES 调整安全策略
4. 建议在程序的关键位置调用安全检测函数
5. 使用前请确保安装所需依赖：psutil、wmi、pythoncom

安全等级评估：7/10（中等偏上）
- 优点：多层防护体系、检测方法多样、编码保护、迷惑技术、配置化管理
- 缺点：加密强度不足、Python语言限制、存在绕过点

改进建议：
1. 使用更强的加密算法（AES等）
2. 移除开发模式开关或加密保护
3. 分离安全代码到多个模块
4. 增加代码混淆
5. 使用HTTPS通信
6. 添加完整性校验
"""


if __name__ == "__main__":
    # 演示程序
    print("高级安全保护模块演示")
    print("=" * 50)

    try:
        # 初始化安全保护
        print("1. 初始化安全保护...")
        security_manager = initialize_security()

        # 获取机器码
        print("2. 获取机器码...")
        machine_code = get_machine_code()
        print(f"   机器码: {machine_code}")

        # 演示许可证验证（使用测试数据）
        print("3. 演示许可证验证...")
        test_license = "test_license_key_123456"
        success, expiry, message = verify_license(test_license, fast_mode=True)
        print(f"   验证结果: {message}")

        print("\n演示完成！")

    except Exception as e:
        print(f"演示过程中发生错误: {str(e)}")

    print("\n请参考文档中的使用示例来集成到您的项目中。")

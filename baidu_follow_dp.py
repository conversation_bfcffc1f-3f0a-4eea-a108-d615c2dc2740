#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百家号关注工具 - DrissionPage版本
使用DrissionPage实现账号登录、ID获取和关注功能
支持批量处理、多线程关注、相互关注等功能
"""

import os
import time
import re
import json
import requests
import urllib3
import threading
import queue
import sys
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from DrissionPage import ChromiumPage
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import shutil

# 导入安全保护模块
try:
    from advanced_security import show_license_window, initialize_security, verify_license, load_license_file
    SECURITY_AVAILABLE = True
    print("✅ 安全保护模块加载成功")
except ImportError as e:
    SECURITY_AVAILABLE = False
    print(f"⚠️ 安全保护模块未找到: {e}")
    print("程序将在无保护模式下运行")

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志系统
def setup_logging():
    """配置日志系统，保持原有输出格式"""
    # 创建自定义格式化器，只输出消息内容
    class SimpleFormatter(logging.Formatter):
        def format(self, record):
            return record.getMessage()

    # 配置根日志器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 如果已有处理器，先清除
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(SimpleFormatter())
    logger.addHandler(console_handler)

    return logger

# 初始化日志系统
logger = setup_logging()

# 日志包装函数，保持原有的print风格
def log_info(message):
    """信息日志"""
    logger.info(message)

def log_warning(message):
    """警告日志"""
    logger.warning(message)

def log_error(message):
    """错误日志"""
    logger.error(message)

def log_success(message):
    """成功日志"""
    logger.info(message)

# 为了保持兼容性，重定义print函数
original_print = print
def print(*args, **kwargs):
    """重定义print函数，使用日志系统"""
    _ = kwargs  # 忽略未使用的参数
    message = ' '.join(str(arg) for arg in args)
    logger.info(message)


# 常量定义
class Constants:
    """应用常量定义"""
    # 网络相关
    DEFAULT_TIMEOUT = 15
    COOKIE_TIMEOUT = 10
    ELEMENT_TIMEOUT = 8
    FAST_ELEMENT_TIMEOUT = 3
    QUICK_ELEMENT_TIMEOUT = 1

    # 线程相关
    DEFAULT_MAX_THREADS = 10
    MIN_THREADS = 1
    MAX_THREADS = 20

    # 时间相关
    DEFAULT_SLEEP_TIME = 0.5
    QUICK_SLEEP_TIME = 0.2
    LONG_SLEEP_TIME = 2
    PROGRESS_UPDATE_INTERVAL = 0.5
    CONFIG_SAVE_DELAY = 1000  # 毫秒
    TEXT_CLEAN_DELAY = 50     # 毫秒

    # 页面等待相关
    MAX_WAIT_TIME = 10
    WAIT_INTERVAL = 0.5

    # ID相关
    MIN_ID_LENGTH = 10
    LONG_NUMBER_MIN_LENGTH = 13
    LONG_NUMBER_MAX_LENGTH = 16

    # UI相关
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    TREE_HEIGHT = 15
    TEXT_AREA_HEIGHT = 8
    TEXT_AREA_WIDTH = 80
    PROGRESS_BAR_LENGTH = 400

    # 文件相关
    CONFIG_FILE = "baidu_follow_config.json"

    # URL相关
    FOLLOW_API = "https://ext.baidu.com/api/subscribe/v1/relation/receive"
    ACCOUNT_SETTINGS_URL = "https://baijiahao.baidu.com/builder/rc/settings/accountSet"
    BAIDU_URL = "https://www.baidu.com"
    BAIJIAHAO_REFERER = "https://baijiahao.baidu.com/"

    # 用户代理
    USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

    # XPath选择器
    ID_XPATH = '//*[@id="layout-main-content"]/div[2]/div/div/div/div[1]/div[1]/div[3]/div[2]/div[1]/div[2]/div/span'
    NAME_XPATH = '//*[@id="personCenterBaseInfo"]/div[2]/div[2]/div[1]'

    # 正则表达式模式
    URL_ID_PATTERNS = [
        r'app_id=(\d+)',
        r'appId=(\d+)',
        r'author_id=(\d+)',
        r'authorId=(\d+)'
    ]

    # Cookie相关
    KEY_COOKIES = ['BDUSS', 'STOKEN', 'bjhStoken', 'devStoken']
    COOKIE_DOMAIN = '.baidu.com'
    COOKIE_PATH = '/'

    # 文件格式
    COOKIE_FILE_EXTENSION = '.txt'

    # 占位符文本
    PLACEHOLDER_TEXT = "请输入要关注的百家号ID，每行一个\n例如：\n1793970813566348\n1234567890123456\n9876543210987654"


class BaiduFollowDP:
    def __init__(self):
        """
        初始化百家号关注工具
        """
        self.page = None
        self.accounts = []  # 存储账号信息 [{'name': '', 'id': '', 'cookies': {}, 'file_path': ''}]
        self.follow_api = Constants.FOLLOW_API
        self.account_settings_url = Constants.ACCOUNT_SETTINGS_URL
        self.max_threads = Constants.DEFAULT_MAX_THREADS  # 最大线程数

    def _set_cookies_to_browser(self, cookies):
        """
        设置Cookie到浏览器的通用方法
        """
        success_count = 0
        for name, value in cookies.items():
            try:
                self.page.run_cdp('Network.setCookie',
                                name=name,
                                value=value,
                                domain=Constants.COOKIE_DOMAIN,
                                path=Constants.COOKIE_PATH,
                                httpOnly=False,
                                secure=False)
                success_count += 1
            except Exception as e:
                print(f"⚠️ 设置Cookie失败 {name}: {e}")
                pass
        return success_count

    def _extract_id_from_url(self, url):
        """
        从URL中提取ID的通用方法
        """
        for pattern in Constants.URL_ID_PATTERNS:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None

    def init_browser(self):
        """
        初始化浏览器
        """
        try:
            print("🌐 正在启动浏览器...")

            # 尝试直接创建ChromiumPage（DrissionPage的正确用法）
            try:
                self.page = ChromiumPage()
                print("✅ 浏览器启动成功")

                # 设置用户代理
                try:
                    self.page.run_cdp('Network.setUserAgentOverride',
                                    userAgent=Constants.USER_AGENT)
                    print("✅ 用户代理设置成功")
                except Exception as e:
                    print(f"⚠️  用户代理设置失败: {e}")

                return True

            except Exception as e:
                print(f"❌ 浏览器启动失败: {e}")
                print("💡 请确保已安装Chrome浏览器并且没有被其他程序占用")
                return False

        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            return False

    def parse_cookie_file(self, cookie_file_path):
        """
        解析Cookie文件，提取账号信息和Cookie数据
        支持多种格式，包括多个cookie的文件
        """
        try:
            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 检查是否是多cookie文件（每行一个cookie）
            lines = content.split('\n')
            if len(lines) > 1:
                # 检查是否每行都包含cookie格式
                cookie_lines = []
                for line in lines:
                    line = line.strip()
                    if line and ('BDUSS=' in line or '----' in line):
                        cookie_lines.append(line)

                if len(cookie_lines) > 1:
                    # 这是一个多cookie文件，返回所有cookie
                    return self.parse_multi_cookie_content(cookie_lines)

            # 单cookie文件处理
            return self.parse_single_cookie_content(content)

        except Exception as e:
            print(f"❌ 解析Cookie文件失败: {e}")
            return {
                'saved_id': None,
                'saved_name': None,
                'cookies': {},
                'has_key_cookies': False,
                'cookie_count': 0,
                'is_multi_cookie': False,
                'all_cookies': []
            }

    def parse_multi_cookie_content(self, cookie_lines):
        """
        解析多cookie内容
        """
        all_cookies = []

        for i, line in enumerate(cookie_lines):
            line = line.strip()
            if not line:
                continue

            # 解析单个cookie行
            single_result = self.parse_single_cookie_content(line)
            if single_result['has_key_cookies']:
                single_result['cookie_index'] = i
                all_cookies.append(single_result)

        # 返回第一个有效cookie的信息，但保留所有cookie
        if all_cookies:
            first_cookie = all_cookies[0]
            return {
                'saved_id': first_cookie['saved_id'],
                'saved_name': first_cookie['saved_name'],
                'cookies': first_cookie['cookies'],
                'has_key_cookies': True,
                'cookie_count': first_cookie['cookie_count'],
                'is_multi_cookie': True,
                'all_cookies': all_cookies
            }
        else:
            return {
                'saved_id': None,
                'saved_name': None,
                'cookies': {},
                'has_key_cookies': False,
                'cookie_count': 0,
                'is_multi_cookie': True,
                'all_cookies': []
            }

    def parse_single_cookie_content(self, content):
        """
        解析单个cookie内容
        """
        # 提取已保存的ID信息
        saved_id = None
        saved_name = None

        # 检查是否有ID信息
        if content.startswith('ID:'):
            lines = content.split('\n')
            id_line = lines[0]
            saved_id = id_line.replace('ID:', '').strip()
            content = '\n'.join(lines[1:])  # 移除ID行

        # 解析不同格式的Cookie文件
        cookie_content = ""

        # 格式1: 账号名----@@@@----Cookie内容----登录成功----账号名----
        if '----@@@@----' in content:
            parts = content.split('----@@@@----')
            if len(parts) >= 2:
                saved_name = parts[0].strip()
                cookie_part = parts[1]

                # 进一步处理Cookie部分
                if '----登录成功----' in cookie_part:
                    cookie_sections = cookie_part.split('----登录成功----')
                    cookie_content = cookie_sections[0].strip()

                    # 如果没有从前面获取到名称，尝试从登录成功后面获取
                    if not saved_name and len(cookie_sections) > 1:
                        name_part = cookie_sections[1].strip()
                        # 移除可能的----符号
                        saved_name = name_part.replace('----', '').strip()
                else:
                    cookie_content = cookie_part.strip()

        # 格式2: 账号----Cookie内容 (新增支持)
        elif '----' in content and not '@@@@' in content:
            parts = content.split('----', 1)
            if len(parts) >= 2:
                saved_name = parts[0].strip()
                cookie_content = parts[1].strip()

        # 格式3: 纯Cookie内容
        else:
            cookie_content = content.strip()

        # 解析Cookie
        cookies = {}
        if cookie_content:
            # 清理Cookie内容，移除可能的特殊字符
            cookie_content = cookie_content.replace('\n', '').replace('\r', '').replace('\t', '')

            # 处理分号分隔的Cookie
            cookie_pairs = cookie_content.split(';')

            for pair in cookie_pairs:
                pair = pair.strip()
                if '=' in pair and len(pair) > 3:  # 确保是有效的Cookie对
                    try:
                        name, value = pair.split('=', 1)
                        name = name.strip()
                        value = value.strip()

                        # 过滤掉明显无效的Cookie
                        if name and value and len(name) > 0 and len(value) > 0:
                            cookies[name] = value
                    except:
                        continue

        # 检查关键Cookie是否存在
        has_key_cookies = any(key in cookies for key in Constants.KEY_COOKIES)

        return {
            'saved_id': saved_id,
            'saved_name': saved_name,
            'cookies': cookies,
            'has_key_cookies': has_key_cookies,
            'cookie_count': len(cookies),
            'is_multi_cookie': False,
            'all_cookies': []
        }

    def load_cookies_from_file(self, cookie_file_path):
        """
        从文件加载Cookie并设置到浏览器
        """
        try:
            print(f"🍪 正在加载Cookie: {cookie_file_path}")

            if not os.path.exists(cookie_file_path):
                print(f"❌ Cookie文件不存在: {cookie_file_path}")
                return False, {}

            # 解析Cookie文件
            parsed_data = self.parse_cookie_file(cookie_file_path)
            cookies = parsed_data['cookies']

            if not parsed_data['has_key_cookies']:
                print(f"❌ Cookie文件缺少关键认证信息")
                return False, {}

            print(f"📊 解析出 {len(cookies)} 个Cookie")

            # 先访问百度建立域名上下文
            self.page.get(Constants.BAIDU_URL)
            time.sleep(Constants.DEFAULT_SLEEP_TIME)  # 进一步减少等待时间

            # 设置Cookie
            success_count = self._set_cookies_to_browser(cookies)
            print(f"✅ 成功设置 {success_count} 个Cookie")
            return success_count > 0, cookies

        except Exception as e:
            print(f"❌ Cookie加载失败: {e}")
            return False, {}

    def load_cookies_from_parsed_data(self, parsed_data):
        """
        从解析后的数据直接加载Cookie到浏览器
        """
        try:
            cookies = parsed_data['cookies']

            if not parsed_data['has_key_cookies']:
                print(f"❌ Cookie数据缺少关键认证信息")
                return False

            print(f"📊 加载 {len(cookies)} 个Cookie")

            # 先访问百度建立域名上下文
            self.page.get(Constants.BAIDU_URL)
            time.sleep(Constants.DEFAULT_SLEEP_TIME)

            # 设置Cookie
            success_count = self._set_cookies_to_browser(cookies)
            print(f"✅ 成功设置 {success_count} 个Cookie")
            return success_count > 0

        except Exception as e:
            print(f"❌ Cookie加载失败: {e}")
            return False

    def save_multi_cookie_account(self, original_file, account_info, cookie_index):
        """
        为多cookie文件中的账号创建单独的文件
        """
        try:
            # 生成新文件名
            base_name = os.path.splitext(original_file)[0]
            extension = os.path.splitext(original_file)[1]
            new_file_name = f"{base_name}_#{cookie_index + 1}_{account_info['name']}{extension}"

            # 读取原始cookie内容
            with open(original_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if cookie_index < len(lines):
                cookie_line = lines[cookie_index].strip()

                # 创建新文件内容
                new_content = f"ID:{account_info['id']}\nNAME:{account_info['name']}\n{cookie_line}"

                # 写入新文件
                with open(new_file_name, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                print(f"💾 多Cookie账号信息已保存到: {os.path.basename(new_file_name)}")
                return new_file_name

        except Exception as e:
            print(f"❌ 保存多Cookie账号信息失败: {e}")
            return None

    def get_account_name_and_id(self, fast_mode=True):
        """
        获取当前账号的名称和ID（改进版本，确保等待页面加载完成）
        """
        try:
            print("🔍 正在获取账号信息...")

            # 访问账号设置页面
            self.page.get(self.account_settings_url)

            if fast_mode:
                # 快速模式：但确保页面完全加载
                account_info = {'name': None, 'id': None}

                # 同时尝试多种方法获取ID（最快）
                try:
                    # 方法1: 直接从URL获取（最快）
                    current_url = self.page.url
                    extracted_id = self._extract_id_from_url(current_url)
                    if extracted_id:
                        account_info['id'] = extracted_id
                        print(f"✅ 从URL快速获取ID: {account_info['id']}")
                except Exception as e:
                    print(f"⚠️ 获取ID元素失败: {e}")
                    pass

                # 等待页面关键元素加载完成，确保ID元素出现
                print("⏳ 等待页面加载完成...")
                max_wait_time = Constants.MAX_WAIT_TIME  # 最多等待时间
                wait_interval = Constants.WAIT_INTERVAL  # 检查间隔
                waited_time = 0

                id_xpath = Constants.ID_XPATH
                name_xpath = Constants.NAME_XPATH

                # 循环等待直到找到ID元素或超时
                while waited_time < max_wait_time:
                    try:
                        # 检查ID元素是否存在且有内容
                        id_element = self.page.ele(f'xpath:{id_xpath}', timeout=Constants.QUICK_ELEMENT_TIMEOUT)
                        if id_element and id_element.text.strip():
                            account_id = id_element.text.strip()
                            if account_id.isdigit() and len(account_id) >= Constants.MIN_ID_LENGTH:
                                account_info['id'] = account_id
                                print(f"✅ 等待后获取到ID: {account_id}")
                                break
                    except:
                        pass

                    time.sleep(wait_interval)
                    waited_time += wait_interval
                    print(f"⏳ 继续等待ID元素... ({waited_time:.1f}s/{max_wait_time}s)")

                # 获取账号名称
                try:
                    name_element = self.page.ele(f'xpath:{name_xpath}', timeout=Constants.FAST_ELEMENT_TIMEOUT)
                    if name_element and name_element.text.strip():
                        account_info['name'] = name_element.text.strip()
                        print(f"✅ 获取账号名称: {account_info['name']}")
                except:
                    print("⚠️  获取账号名称失败")

                # 如果还没获取到ID，尝试备用方法
                if not account_info['id']:
                    print("🔄 尝试备用方法获取ID...")
                    try:
                        # 备用方法1: 从页面源码中查找
                        page_html = self.page.html
                        pattern = f'\\b\\d{{{Constants.LONG_NUMBER_MIN_LENGTH},{Constants.LONG_NUMBER_MAX_LENGTH}}}\\b'
                        long_numbers = re.findall(pattern, page_html)
                        if long_numbers:
                            account_info['id'] = long_numbers[0]
                            print(f"✅ 从页面源码获取ID: {account_info['id']}")
                    except Exception as e:
                        print(f"⚠️ 从页面源码获取ID失败: {e}")
                        pass

                # 最终检查
                if account_info['id']:
                    print(f"🎉 成功获取账号信息: 名称={account_info['name']}, ID={account_info['id']}")
                else:
                    print("❌ 未能获取到账号ID")

                return account_info

            else:
                # 原有的慢速但稳定的方法
                time.sleep(Constants.LONG_SLEEP_TIME)
                account_info = {'name': None, 'id': None}

                # 获取账号名称
                try:
                    name_xpath = '//*[@id="personCenterBaseInfo"]/div[2]/div[2]/div[1]'
                    name_element = self.page.ele(f'xpath:{name_xpath}', timeout=8)
                    if name_element:
                        account_name = name_element.text.strip()
                        account_info['name'] = account_name
                        print(f"✅ 获取账号名称: {account_name}")
                except Exception as e:
                    print(f"⚠️  获取账号名称失败: {e}")

                # 获取账号ID
                try:
                    id_xpath = '//*[@id="layout-main-content"]/div[2]/div/div/div/div[1]/div[1]/div[3]/div[2]/div[1]/div[2]/div/span'
                    id_element = self.page.ele(f'xpath:{id_xpath}', timeout=8)
                    if id_element:
                        account_id = id_element.text.strip()
                        if account_id and account_id.isdigit():
                            account_info['id'] = account_id
                            print(f"✅ 获取账号ID: {account_id}")
                except Exception as e:
                    print(f"⚠️  获取账号ID失败: {e}")

                return account_info

        except Exception as e:
            print(f"❌ 获取账号信息失败: {e}")
            return {'name': None, 'id': None}

    def batch_process_cookies(self, cookie_folder, progress_callback=None, update_callback=None):
        """
        批量处理Cookie文件夹中的所有Cookie文件
        支持预加载已有ID和实时显示
        """
        try:
            print(f"📁 开始批量处理Cookie文件夹: {cookie_folder}")

            # 获取所有txt文件
            cookie_files = []
            for file in os.listdir(cookie_folder):
                if file.endswith('.txt'):
                    cookie_files.append(os.path.join(cookie_folder, file))

            if not cookie_files:
                print("❌ 文件夹中没有找到Cookie文件")
                return []

            print(f"📋 找到 {len(cookie_files)} 个Cookie文件")

            # 第一阶段：预加载所有文件，检查已有ID
            print("🔍 第一阶段：预加载文件信息...")
            preloaded_accounts = []
            need_login_files = []

            for i, cookie_file in enumerate(cookie_files, 1):
                try:
                    if progress_callback:
                        progress_callback(f"预加载第 {i}/{len(cookie_files)} 个文件...")

                    # 解析Cookie文件
                    parsed_data = self.parse_cookie_file(cookie_file)

                    if parsed_data['saved_id'] and parsed_data['saved_name']:
                        # 已有完整信息，直接添加
                        account_record = {
                            'name': parsed_data['saved_name'],
                            'id': parsed_data['saved_id'],
                            'cookies': parsed_data['cookies'],
                            'file_path': cookie_file,
                            'original_file': os.path.basename(cookie_file),
                            'source': 'cached'
                        }
                        preloaded_accounts.append(account_record)
                        print(f"✅ 预加载: {parsed_data['saved_name']} (ID: {parsed_data['saved_id']})")

                        # 实时更新显示
                        if update_callback:
                            update_callback(account_record)

                    elif parsed_data['has_key_cookies']:
                        # 有Cookie但没有ID，需要登录获取
                        need_login_files.append({
                            'file_path': cookie_file,
                            'parsed_data': parsed_data
                        })
                        print(f"⏳ 需要登录: {os.path.basename(cookie_file)}")

                    else:
                        print(f"❌ 无效Cookie文件: {os.path.basename(cookie_file)}")

                except Exception as e:
                    print(f"❌ 预加载失败 {cookie_file}: {e}")
                    continue

            print(f"📊 预加载完成: {len(preloaded_accounts)} 个已有信息, {len(need_login_files)} 个需要登录")

            # 第二阶段：登录获取剩余账号信息
            if need_login_files:
                print("🌐 第二阶段：登录获取账号信息...")

                for i, file_info in enumerate(need_login_files, 1):
                    try:
                        cookie_file = file_info['file_path']
                        parsed_data = file_info['parsed_data']

                        if progress_callback:
                            progress_callback(f"登录第 {i}/{len(need_login_files)} 个账号...")

                        print(f"\n🔐 [{i}/{len(need_login_files)}] 登录: {os.path.basename(cookie_file)}")

                        # 加载Cookie
                        success, cookies = self.load_cookies_from_file(cookie_file)
                        if not success:
                            print(f"❌ Cookie加载失败: {cookie_file}")
                            continue

                        # 快速获取账号信息
                        account_info = self.get_account_name_and_id(fast_mode=True)

                        if account_info['name'] and account_info['id']:
                            # 创建账号记录
                            account_record = {
                                'name': account_info['name'],
                                'id': account_info['id'],
                                'cookies': cookies,
                                'file_path': cookie_file,
                                'original_file': os.path.basename(cookie_file),
                                'source': 'login'
                            }

                            preloaded_accounts.append(account_record)

                            # 实时更新显示
                            if update_callback:
                                update_callback(account_record)

                            # 保存ID到Cookie文件中
                            self.save_id_to_cookie_file(cookie_file, account_info['id'], account_info['name'])

                            # 重命名文件为账号名
                            new_file_path = self.rename_cookie_file(cookie_file, account_info['name'])
                            if new_file_path:
                                account_record['file_path'] = new_file_path

                            print(f"✅ 登录成功: {account_info['name']} (ID: {account_info['id']})")
                        else:
                            print(f"❌ 账号信息获取失败: {cookie_file}")

                        # 极短延迟
                        time.sleep(0.2)

                    except Exception as e:
                        print(f"❌ 登录处理失败 {cookie_file}: {e}")
                        continue

            print(f"\n🎉 批量处理完成: {len(preloaded_accounts)}/{len(cookie_files)} 个账号成功")
            return preloaded_accounts

        except Exception as e:
            print(f"❌ 批量处理失败: {e}")
            return []

    def save_id_to_cookie_file(self, cookie_file, account_id, account_name=None):
        """
        将账号ID和名称保存到Cookie文件中
        """
        try:
            with open(cookie_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 检查是否已经包含ID信息
            if not content.startswith('ID:'):
                # 在文件开头添加ID信息
                id_line = f"ID:{account_id}"
                if account_name:
                    id_line += f"\nNAME:{account_name}"

                new_content = f"{id_line}\n{content}"

                with open(cookie_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                print(f"💾 信息已保存到文件: ID={account_id}, NAME={account_name}")

        except Exception as e:
            print(f"❌ 保存信息到文件失败: {e}")

    def rename_cookie_file(self, cookie_file, account_name):
        """
        将Cookie文件重命名为账号名
        """
        try:
            # 清理账号名中的非法字符
            safe_name = re.sub(r'[<>:"/\\|?*]', '_', account_name)

            # 构建新文件路径
            file_dir = os.path.dirname(cookie_file)
            new_file_path = os.path.join(file_dir, f"{safe_name}.txt")

            # 如果新文件名已存在，添加序号
            counter = 1
            while os.path.exists(new_file_path):
                new_file_path = os.path.join(file_dir, f"{safe_name}_{counter}.txt")
                counter += 1

            # 重命名文件
            shutil.move(cookie_file, new_file_path)
            print(f"📝 文件已重命名: {os.path.basename(new_file_path)}")

            return new_file_path

        except Exception as e:
            print(f"❌ 文件重命名失败: {e}")
            return cookie_file

    def get_account_id(self):
        """
        获取当前账号的百家号ID
        """
        try:
            print("🔍 正在获取账号ID...")

            # 访问账号设置页面
            self.page.get(self.account_settings_url)
            time.sleep(2)  # 减少等待时间

            print(f"📍 当前URL: {self.page.url}")
            print(f"📄 页面标题: {self.page.title}")

            # 检查是否成功登录
            if "登录" in self.page.title or "login" in self.page.url.lower():
                print("❌ 未登录或登录失败")
                return None

            # 方法1: 使用指定的元素路径获取ID
            try:
                xpath = '//*[@id="layout-main-content"]/div[2]/div/div/div/div[1]/div[1]/div[3]/div[2]/div[1]/div[2]/div/span'
                print(f"🎯 使用指定XPath获取ID: {xpath}")

                # 等待元素出现
                element = self.page.ele(f'xpath:{xpath}', timeout=10)
                if element:
                    account_id = element.text.strip()
                    if account_id and account_id.isdigit():
                        print(f"✅ 从指定元素获取到ID: {account_id}")
                        return account_id
                    else:
                        print(f"⚠️  元素文本不是有效ID: {account_id}")
                else:
                    print("❌ 未找到指定元素")
            except Exception as e:
                print(f"⚠️  XPath获取失败: {e}")

            # 方法2: 尝试其他可能的元素选择器
            try:
                print("🔍 尝试其他元素选择器...")

                # 常见的ID显示元素
                selectors = [
                    'span:contains("1")',  # 包含数字的span
                    '.account-id',
                    '.user-id',
                    '[data-id]',
                    'span[title]'
                ]

                for selector in selectors:
                    try:
                        elements = self.page.eles(selector, timeout=2)
                        for element in elements:
                            text = element.text.strip()
                            if text and len(text) >= 13 and text.isdigit():
                                print(f"✅ 从选择器 {selector} 获取到ID: {text}")
                                return text
                    except Exception as e:
                        print(f"⚠️ 选择器 {selector} 查找失败: {e}")
                        continue

            except Exception as e:
                print(f"⚠️  选择器方法失败: {e}")

            # 方法3: 从URL中提取ID（快速）
            current_url = self.page.url
            url_patterns = [
                r'app_id=(\d+)',
                r'appId=(\d+)',
                r'author_id=(\d+)',
                r'authorId=(\d+)'
            ]

            for pattern in url_patterns:
                match = re.search(pattern, current_url)
                if match:
                    account_id = match.group(1)
                    print(f"✅ 从URL提取到ID: {account_id}")
                    return account_id

            # 方法4: 从页面源码快速提取
            print("🔍 从页面源码提取...")
            page_html = self.page.html

            # 查找长数字（13-16位）
            long_numbers = re.findall(r'\b\d{13,16}\b', page_html)
            if long_numbers:
                # 去重并选择第一个
                unique_numbers = list(set(long_numbers))
                account_id = unique_numbers[0]
                print(f"✅ 从页面源码提取到ID: {account_id}")
                return account_id

            print("❌ 所有方法都未能获取到账号ID")
            return None

        except Exception as e:
            print(f"❌ 获取账号ID失败: {e}")
            return None

    def follow_account_by_id(self, target_id, cookies, account_name="未知账号"):
        """
        通过API关注指定ID的账号
        """
        try:
            # 创建session
            session = requests.Session()
            session.verify = False

            # 设置请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://baijiahao.baidu.com/',
                'Connection': 'keep-alive',
            })

            # 设置Cookie
            for name, value in cookies.items():
                session.cookies.set(name, value, domain='.baidu.com')

            # 构建请求参数
            params = {
                'type': 'media',
                'op_type': 'add',
                'third_id': str(target_id),
                'sfrom': 'media',
                'source': 'media',
                'store': 'uid_cuid',
                'sid': '',
                'position': '',
                'callback': f'd{int(time.time() * 1000)}'
            }

            # 发送关注请求
            response = session.get(self.follow_api, params=params, timeout=10)

            if response.status_code == 200:
                content = response.text

                # 解析JSONP响应
                if content.startswith(params['callback']):
                    json_start = content.find('(') + 1
                    json_end = content.rfind(')')
                    if json_start > 0 and json_end > json_start:
                        json_content = content[json_start:json_end]
                        try:
                            data = json.loads(json_content)
                            if data.get('errno') == 0:
                                return True, f"{account_name} 关注成功"
                            else:
                                error_msg = data.get('errmsg', '未知错误')
                                return False, f"{account_name} 关注失败: {error_msg}"
                        except json.JSONDecodeError as e:
                            print(f"⚠️ JSON解析失败: {e}")
                            pass

                # 检查其他成功标识
                if '"errno":0' in content:
                    return True, f"{account_name} 关注成功"
                else:
                    return False, f"{account_name} 关注失败"
            else:
                return False, f"{account_name} 请求失败: {response.status_code}"

        except Exception as e:
            return False, f"{account_name} 关注异常: {e}"

    def mutual_follow_threaded(self, progress_callback=None):
        """
        多线程相互关注
        """
        if len(self.accounts) < 2:
            return False, "需要至少2个账号才能进行相互关注"

        print(f"🚀 开始多线程相互关注，账号数: {len(self.accounts)}")

        # 创建关注任务列表
        follow_tasks = []
        for follower in self.accounts:
            for target in self.accounts:
                if follower['id'] != target['id']:  # 不关注自己
                    follow_tasks.append({
                        'follower': follower,
                        'target_id': target['id'],
                        'target_name': target['name']
                    })

        print(f"📋 总共 {len(follow_tasks)} 个关注任务")

        # 执行多线程关注
        return self._execute_follow_tasks(follow_tasks, progress_callback)

    def batch_follow_threaded(self, target_ids, progress_callback=None):
        """
        多线程批量关注
        """
        if not self.accounts:
            return False, "没有可用的账号"

        if not target_ids:
            return False, "没有要关注的目标ID"

        print(f"🚀 开始多线程批量关注，账号数: {len(self.accounts)}, 目标数: {len(target_ids)}")

        # 创建关注任务列表
        follow_tasks = []
        for follower in self.accounts:
            for target_id in target_ids:
                if follower['id'] != target_id:  # 不关注自己
                    follow_tasks.append({
                        'follower': follower,
                        'target_id': target_id,
                        'target_name': f"ID_{target_id}"
                    })

        print(f"📋 总共 {len(follow_tasks)} 个关注任务")

        # 执行多线程关注
        return self._execute_follow_tasks(follow_tasks, progress_callback)

    def _execute_follow_tasks(self, follow_tasks, progress_callback=None):
        """
        执行关注任务（多线程）
        """
        try:
            results = []
            completed_count = 0
            last_progress_update = 0  # 记录上次更新进度的时间

            def follow_worker(task):
                """工作线程函数"""
                nonlocal completed_count, last_progress_update

                follower = task['follower']
                target_id = task['target_id']
                target_name = task['target_name']

                success, message = self.follow_account_by_id(
                    target_id,
                    follower['cookies'],
                    follower['name']
                )

                completed_count += 1

                # 减少进度更新频率，避免GUI卡死
                current_time = time.time()
                if progress_callback and (current_time - last_progress_update > 0.5 or completed_count == len(follow_tasks)):
                    progress = (completed_count / len(follow_tasks)) * 100
                    main_message = f"关注进度: {completed_count}/{len(follow_tasks)}"
                    detail_message = f"成功率: {progress:.1f}% | 当前: {follower['name']} → {target_name}"
                    progress_callback(main_message, progress, detail_message)
                    last_progress_update = current_time

                return {
                    'follower_name': follower['name'],
                    'follower_id': follower['id'],
                    'target_id': target_id,
                    'target_name': target_name,
                    'success': success,
                    'message': message,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }

            # 使用线程池执行任务
            with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
                # 提交所有任务
                future_to_task = {executor.submit(follow_worker, task): task for task in follow_tasks}

                # 收集结果
                for future in as_completed(future_to_task):
                    try:
                        result = future.result()
                        results.append(result)

                        # 输出日志
                        status = "✅" if result['success'] else "❌"
                        print(f"{status} {result['follower_name']} → {result['target_name']}: {result['message']}")

                    except Exception as e:
                        print(f"❌ 任务执行异常: {e}")

            # 统计结果
            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            summary = {
                'total_tasks': total_count,
                'success_count': success_count,
                'success_rate': success_rate,
                'results': results,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            print(f"\n📊 多线程关注完成:")
            print(f"   总任务数: {total_count}")
            print(f"   成功数: {success_count}")
            print(f"   成功率: {success_rate:.1f}%")

            return True, summary

        except Exception as e:
            print(f"❌ 多线程关注失败: {e}")
            return False, str(e)

    def follow_account_by_id_old(self, target_id, cookies):
        """
        通过API关注指定ID的账号
        """
        try:
            print(f"👥 正在关注账号ID: {target_id}")
            
            # 创建session
            session = requests.Session()
            session.verify = False
            
            # 设置请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://baijiahao.baidu.com/',
                'Connection': 'keep-alive',
            })
            
            # 设置Cookie
            for name, value in cookies.items():
                session.cookies.set(name, value, domain='.baidu.com')
            
            # 构建请求参数
            params = {
                'type': 'media',
                'op_type': 'add',
                'third_id': str(target_id),
                'sfrom': 'media',
                'source': 'media',
                'store': 'uid_cuid',
                'sid': '',
                'position': '',
                'callback': f'd{int(time.time() * 1000)}'
            }
            
            # 发送关注请求
            response = session.get(self.follow_api, params=params, timeout=15)
            
            if response.status_code == 200:
                content = response.text
                print(f"📄 API响应: {content}")
                
                # 解析JSONP响应
                if content.startswith(params['callback']):
                    json_start = content.find('(') + 1
                    json_end = content.rfind(')')
                    if json_start > 0 and json_end > json_start:
                        json_content = content[json_start:json_end]
                        try:
                            data = json.loads(json_content)
                            if data.get('errno') == 0:
                                print(f"✅ 关注成功: {target_id}")
                                return True, "关注成功"
                            else:
                                error_msg = data.get('errmsg', '未知错误')
                                print(f"❌ 关注失败: {error_msg}")
                                return False, error_msg
                        except:
                            pass
                
                # 检查其他成功标识
                if '"errno":0' in content:
                    print(f"✅ 关注成功: {target_id}")
                    return True, "关注成功"
                else:
                    print(f"❌ 关注失败: {target_id}")
                    return False, "关注失败"
            else:
                error_msg = f"请求失败: {response.status_code}"
                print(f"❌ {error_msg}")
                return False, error_msg
                
        except Exception as e:
            error_msg = f"关注异常: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg

    def close_browser(self):
        """
        关闭浏览器
        """
        try:
            if self.page:
                self.page.quit()
                self.page = None  # 清除引用，防止内存泄漏
                print("✅ 浏览器已关闭")
        except Exception as e:
            print(f"❌ 关闭浏览器失败: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        if self.init_browser():
            return self
        else:
            raise RuntimeError("浏览器初始化失败")

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，确保资源清理"""
        _ = exc_tb  # 忽略未使用的参数
        self.close_browser()
        if exc_type:
            print(f"❌ 执行过程中发生异常: {exc_val}")
        return False  # 不抑制异常


class BaiduFollowGUI:
    def __init__(self):
        """
        初始化GUI界面
        """
        self.root = tk.Tk()

        # 根据安全保护状态设置标题
        if SECURITY_AVAILABLE:
            self.root.title("百家号关注工具 v2.0 [已授权]")
        else:
            self.root.title("百家号关注工具 v2.0 [无保护模式]")

        self.root.geometry(f"{Constants.WINDOW_WIDTH}x{Constants.WINDOW_HEIGHT}")

        self.dp_tool = BaiduFollowDP()
        self.cookie_folder = ""
        self.accounts_data = []  # 存储账号数据用于显示
        self.config_file = Constants.CONFIG_FILE  # 配置文件路径

        self.setup_styles()  # 设置样式
        self.setup_ui()
        self.load_config()  # 加载配置

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_styles(self):
        """
        设置UI样式
        """
        style = ttk.Style()

        # 设置进度条样式
        style.configure(
            "Custom.Horizontal.TProgressbar",
            background='#4CAF50',  # 绿色背景
            troughcolor='#E0E0E0',  # 灰色槽
            borderwidth=1,
            lightcolor='#4CAF50',
            darkcolor='#4CAF50'
        )

        # 设置按钮样式 - 保持默认样式
        # 不需要特殊配置，使用系统默认的按钮样式

    def setup_ui(self):
        """
        设置UI界面
        """
        # 创建菜单栏
        self.create_menu_bar()

        # 创建主要的Notebook（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 5))

        # 第一个标签页：账号管理
        account_frame = ttk.Frame(notebook)
        notebook.add(account_frame, text="账号管理")

        # 第二个标签页：关注操作
        follow_frame = ttk.Frame(notebook)
        notebook.add(follow_frame, text="关注操作")

        # 设置账号管理页面
        self.setup_account_tab(account_frame)

        # 设置关注操作页面
        self.setup_follow_tab(follow_frame)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)

        if SECURITY_AVAILABLE:
            help_menu.add_command(label="授权信息", command=self.show_license_info)
            help_menu.add_separator()

        help_menu.add_command(label="关于", command=self.show_about)

    def show_license_info(self):
        """显示授权信息"""
        if not SECURITY_AVAILABLE:
            messagebox.showinfo("提示", "安全保护模块未加载")
            return

        try:
            from advanced_security import load_license_file, get_machine_code, LicenseManager

            license_manager = LicenseManager()
            license_key = load_license_file()
            machine_code = get_machine_code()

            if license_key:
                # 验证当前许可证
                success, expiry, message = license_manager.verify_license(license_key, fast_mode=True)

                if success:
                    info_text = f"""授权信息：

✅ 授权状态：已授权
📅 到期时间：{expiry}
🔑 许可证：{license_key[:8]}****{license_key[-8:] if len(license_key) > 16 else license_key}
🖥️ 机器码：{machine_code}

授权验证成功！"""
                else:
                    info_text = f"""授权信息：

❌ 授权状态：验证失败
📝 错误信息：{message}
🔑 许可证：{license_key[:8]}****{license_key[-8:] if len(license_key) > 16 else license_key}
🖥️ 机器码：{machine_code}

请检查网络连接或联系技术支持。"""
            else:
                info_text = f"""授权信息：

⚠️ 授权状态：未授权
🖥️ 机器码：{machine_code}

请输入有效的许可证进行授权。"""

            messagebox.showinfo("授权信息", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取授权信息失败：{str(e)}")

    def show_about(self):
        """显示关于信息"""
        about_text = """百家号关注工具 v2.0

🚀 功能特性：
• 批量导入百家号账号
• 多线程并发关注
• 智能防检测机制
• 详细操作日志

🛡️ 安全保护：
• 反调试检测
• 反虚拟机检测
• 反抓包检测
• 硬件绑定授权

📧 技术支持：
如有问题请联系作者

© 2024 版权所有"""

        messagebox.showinfo("关于", about_text)

    def create_status_bar(self):
        """创建状态栏"""
        # 移除状态栏显示，不再显示授权信息和机器码
        pass

    def setup_account_tab(self, parent):
        """
        设置账号管理标签页
        """
        # Cookie文件夹选择区域
        folder_frame = ttk.LabelFrame(parent, text="Cookie文件夹", padding="10")
        folder_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        # 配置列权重，让路径显示区域能够扩展
        folder_frame.columnconfigure(1, weight=1)

        ttk.Label(folder_frame, text="Cookie文件夹:").grid(row=0, column=0, sticky=tk.W)
        self.cookie_folder_var = tk.StringVar()

        # 创建一个模拟Entry外观的显示区域，但不会获得焦点
        path_display_frame = tk.Frame(folder_frame, relief='solid', bd=1, bg='white')
        path_display_frame.grid(row=0, column=1, padx=(5, 5), sticky='ew', ipady=2)

        self.cookie_folder_label = tk.Label(
            path_display_frame,
            textvariable=self.cookie_folder_var,
            bg='white',
            fg='black',
            anchor='w',
            padx=5,
            font=('TkDefaultFont', 9)
        )
        self.cookie_folder_label.pack(fill='both', expand=True)



        ttk.Button(folder_frame, text="选择文件夹", command=self.select_cookie_folder).grid(row=0, column=2)

        # 操作按钮区域
        button_frame = ttk.Frame(folder_frame)
        button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))

        ttk.Button(button_frame, text="获取所有账号ID", command=self.batch_get_accounts).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空列表", command=self.clear_accounts).pack(side=tk.LEFT)

        # 进度显示
        self.progress_var = tk.StringVar(value="等待操作...")
        ttk.Label(folder_frame, textvariable=self.progress_var).grid(row=2, column=0, columnspan=3, pady=(5, 0))

        # 账号列表区域
        list_frame = ttk.LabelFrame(parent, text="账号列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建Treeview显示账号信息
        columns = ('账号名称', '百家号ID', '状态')
        self.account_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题和居中对齐
        for col in columns:
            self.account_tree.heading(col, text=col, anchor='center')
            self.account_tree.column(col, width=200, anchor='center')

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.account_tree.yview)
        self.account_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.account_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_follow_tab(self, parent):
        """
        设置关注操作标签页
        """
        # 关注模式选择
        mode_frame = ttk.LabelFrame(parent, text="关注模式", padding="10")
        mode_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        self.follow_mode = tk.StringVar(value="mutual")
        ttk.Radiobutton(mode_frame, text="相互关注（账号间互相关注）", variable=self.follow_mode, value="mutual").pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="批量关注（关注指定ID列表）", variable=self.follow_mode, value="batch").pack(anchor=tk.W)

        # 批量关注ID输入区域
        batch_frame = ttk.LabelFrame(parent, text="批量关注ID列表", padding="10")
        batch_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(batch_frame, text="要关注的ID（一行一个）:").pack(anchor=tk.W)
        self.batch_ids_text = scrolledtext.ScrolledText(batch_frame, width=80, height=8)
        self.batch_ids_text.pack(fill=tk.X, pady=(5, 0))

        # 绑定事件处理，简化Enter键行为
        def on_key_release(event):
            """处理按键释放事件，延迟清理多余的空行"""
            if event.keysym in ['Return', 'KP_Enter']:
                # 使用after延迟执行，让换行操作先完成
                def clean_lines():
                    try:
                        # 记住当前光标位置
                        current_pos = self.batch_ids_text.index(tk.INSERT)

                        # 获取当前内容
                        content = self.batch_ids_text.get("1.0", "end-1c")
                        lines = content.split('\n')

                        # 移除连续的空行，只保留单个空行
                        cleaned_lines = []
                        prev_empty = False

                        for line in lines:
                            line_stripped = line.strip()
                            if line_stripped:  # 非空行
                                cleaned_lines.append(line_stripped)
                                prev_empty = False
                            elif not prev_empty:  # 空行，但前一行不是空行
                                cleaned_lines.append('')
                                prev_empty = True
                            # 如果是连续的空行，则跳过

                        # 移除末尾多余的空行（保留一个）
                        while len(cleaned_lines) > 1 and not cleaned_lines[-1] and not cleaned_lines[-2]:
                            cleaned_lines.pop()

                        # 更新文本框内容
                        new_content = '\n'.join(cleaned_lines)
                        if new_content != content:
                            self.batch_ids_text.delete("1.0", tk.END)
                            self.batch_ids_text.insert("1.0", new_content)
                            # 尝试恢复光标位置
                            try:
                                self.batch_ids_text.mark_set(tk.INSERT, current_pos)
                            except tk.TclError:
                                # 如果位置无效，移到末尾
                                self.batch_ids_text.mark_set(tk.INSERT, tk.END)
                    except Exception as e:
                        print(f"⚠️ 文本清理失败: {e}")
                        pass

                # 延迟50毫秒执行清理
                self.root.after(50, clean_lines)

        self.batch_ids_text.bind('<KeyRelease>', on_key_release)

        # 添加占位符提示
        placeholder_text = "请输入要关注的百家号ID，每行一个\n例如：\n1793970813566348\n1234567890123456\n9876543210987654"
        self.batch_ids_text.insert("1.0", placeholder_text)
        self.batch_ids_text.config(fg='gray')

        def on_focus_in(event=None):
            """获得焦点时清除占位符"""
            _ = event  # 忽略未使用的参数
            if self.batch_ids_text.get("1.0", "end-1c") == placeholder_text:
                self.batch_ids_text.delete("1.0", tk.END)
                self.batch_ids_text.config(fg='black')

        def on_focus_out(event=None):
            """失去焦点时恢复占位符（如果为空）"""
            _ = event  # 忽略未使用的参数
            if not self.batch_ids_text.get("1.0", "end-1c").strip():
                self.batch_ids_text.insert("1.0", placeholder_text)
                self.batch_ids_text.config(fg='gray')

        self.batch_ids_text.bind('<FocusIn>', on_focus_in)
        self.batch_ids_text.bind('<FocusOut>', on_focus_out)

        # 多线程设置
        thread_frame = ttk.LabelFrame(parent, text="多线程设置", padding="10")
        thread_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(thread_frame, text="线程数量:").grid(row=0, column=0, sticky=tk.W)
        self.thread_count_var = tk.StringVar(value="10")

        # 添加变化监听，当线程数改变时保存配置
        def on_thread_count_change(*args):
            _ = args  # 忽略未使用的参数
            try:
                # 延迟保存，避免频繁写入
                if hasattr(self, '_thread_save_job'):
                    self.root.after_cancel(self._thread_save_job)
                self._thread_save_job = self.root.after(1000, self.save_config)  # 1秒后保存
            except Exception as e:
                print(f"⚠️ 保存配置失败: {e}")
                pass

        self.thread_count_var.trace_add('write', on_thread_count_change)

        thread_spinbox = ttk.Spinbox(thread_frame, from_=1, to=20, textvariable=self.thread_count_var, width=10)
        thread_spinbox.grid(row=0, column=1, padx=(5, 0))
        ttk.Label(thread_frame, text="(建议不超过10个，避免被封)").grid(row=0, column=2, padx=(10, 0))

        # 执行按钮
        execute_frame = ttk.Frame(parent)
        execute_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(execute_frame, text="开始关注", command=self.start_follow).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(execute_frame, text="停止关注", command=self.stop_follow).pack(side=tk.LEFT)

        # 关注进度区域
        progress_frame = ttk.LabelFrame(parent, text="关注进度", padding="10")
        progress_frame.pack(fill=tk.X, padx=10, pady=5)

        # 进度条
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='determinate',
            length=400,
            style="Custom.Horizontal.TProgressbar"
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        # 进度文本
        self.follow_progress_var = tk.StringVar(value="等待开始...")
        progress_label = ttk.Label(progress_frame, textvariable=self.follow_progress_var)
        progress_label.pack()

        # 详细状态
        self.detail_progress_var = tk.StringVar(value="")
        detail_label = ttk.Label(progress_frame, textvariable=self.detail_progress_var, font=("Arial", 9), foreground="gray")
        detail_label.pack()

        # 日志区域
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, width=100, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def load_config(self):
        """
        加载配置文件
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                    # 加载文件夹路径
                    last_folder = config.get('last_cookie_folder', '')
                    if last_folder and os.path.exists(last_folder):
                        self.cookie_folder_var.set(last_folder)
                        self.cookie_folder = last_folder
                        self.log(f"已加载上次使用的文件夹: {last_folder}")

                    # 加载线程数设置
                    thread_count = config.get('thread_count', 10)
                    if hasattr(self, 'thread_count_var'):
                        self.thread_count_var.set(str(thread_count))
                        self.log(f"已加载线程数设置: {thread_count}")
        except Exception as e:
            self.log(f"加载配置失败: {e}")

    def save_config(self):
        """
        保存配置文件
        """
        try:
            config = {
                'last_cookie_folder': self.cookie_folder,
                'thread_count': int(self.thread_count_var.get()) if hasattr(self, 'thread_count_var') else 10
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存配置失败: {e}")

    def log(self, message):
        """
        添加日志信息
        """
        timestamp = time.strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()

    def select_cookie_folder(self):
        """
        选择Cookie文件夹
        """
        # 如果有上次的路径，从上次的路径开始选择
        initial_dir = self.cookie_folder if self.cookie_folder and os.path.exists(self.cookie_folder) else ""

        folder_path = filedialog.askdirectory(
            title="选择Cookie文件夹",
            initialdir=initial_dir
        )
        if folder_path:
            self.cookie_folder_var.set(folder_path)
            self.cookie_folder = folder_path
            self.save_config()  # 保存配置
            self.log(f"已选择Cookie文件夹: {folder_path}")

    def batch_get_accounts(self):
        """
        批量获取所有账号ID（支持预加载和实时显示）
        """
        if not self.cookie_folder:
            messagebox.showwarning("警告", "请先选择Cookie文件夹")
            return

        try:
            self.log("开始批量获取账号ID...")
            self.progress_var.set("正在预加载文件信息...")

            # 清空现有列表
            self.clear_accounts()

            # 定义进度回调
            def progress_callback(message):
                self.progress_var.set(message)
                self.root.update()

            # 定义实时更新回调
            def update_callback(account_record):
                # 实时添加到列表显示
                source_text = "已缓存" if account_record.get('source') == 'cached' else "新获取"
                self.account_tree.insert('', tk.END, values=(
                    account_record['name'],
                    account_record['id'],
                    source_text
                ))
                self.accounts_data.append(account_record)
                self.root.update()

                # 记录日志
                self.log(f"✅ [{source_text}] {account_record['name']} (ID: {account_record['id']})")

            # 第一阶段：预加载（不需要浏览器）
            self.log("🔍 第一阶段：检查已保存的账号信息...")

            # 获取所有Cookie文件
            cookie_files = []
            for file in os.listdir(self.cookie_folder):
                if file.endswith('.txt'):
                    cookie_files.append(os.path.join(self.cookie_folder, file))

            if not cookie_files:
                self.log("❌ 文件夹中没有找到Cookie文件")
                messagebox.showwarning("警告", "文件夹中没有找到Cookie文件")
                return

            # 预加载阶段
            need_login_count = 0
            need_login_files = []  # 存储需要登录的文件信息

            for i, cookie_file in enumerate(cookie_files, 1):
                try:
                    progress_callback(f"预加载第 {i}/{len(cookie_files)} 个文件...")

                    # 解析Cookie文件
                    parsed_data = self.dp_tool.parse_cookie_file(cookie_file)

                    if parsed_data['saved_id']:
                        # 已有ID信息，直接添加
                        # 使用文件名（去掉扩展名）作为账号名称
                        account_name = os.path.splitext(os.path.basename(cookie_file))[0]

                        account_record = {
                            'name': account_name,
                            'id': parsed_data['saved_id'],
                            'cookies': parsed_data['cookies'],
                            'file_path': cookie_file,
                            'original_file': os.path.basename(cookie_file),
                            'source': 'cached'
                        }
                        update_callback(account_record)

                    elif parsed_data['has_key_cookies']:
                        # 检查是否是多cookie文件
                        if parsed_data.get('is_multi_cookie', False):
                            # 多cookie文件，每个cookie都需要单独登录
                            for cookie_data in parsed_data['all_cookies']:
                                need_login_files.append({
                                    'file_path': cookie_file,
                                    'parsed_data': cookie_data,
                                    'is_multi_cookie': True,
                                    'cookie_index': cookie_data.get('cookie_index', 0)
                                })
                                need_login_count += 1
                        else:
                            # 单cookie文件
                            need_login_files.append({
                                'file_path': cookie_file,
                                'parsed_data': parsed_data,
                                'is_multi_cookie': False,
                                'cookie_index': 0
                            })
                            need_login_count += 1

                except Exception as e:
                    self.log(f"❌ 预加载失败 {cookie_file}: {e}")
                    continue

            # 第二阶段：登录获取剩余信息
            if need_login_count > 0:
                self.log(f"🌐 第二阶段：需要登录获取 {need_login_count} 个账号信息...")
                self.progress_var.set("正在初始化浏览器...")

                # 初始化浏览器
                if not self.dp_tool.init_browser():
                    self.log("❌ 浏览器初始化失败")
                    messagebox.showerror("错误", "浏览器初始化失败")
                    return

                # 处理需要登录的文件
                login_count = 0
                for file_info in need_login_files:
                    try:
                        cookie_file = file_info['file_path']
                        parsed_data = file_info['parsed_data']
                        is_multi_cookie = file_info['is_multi_cookie']
                        cookie_index = file_info['cookie_index']

                        login_count += 1

                        if is_multi_cookie:
                            progress_callback(f"登录第 {login_count}/{need_login_count} 个账号 (文件: {os.path.basename(cookie_file)}, Cookie #{cookie_index + 1})...")
                            self.log(f"🔐 [{login_count}/{need_login_count}] 登录多Cookie文件中的第 {cookie_index + 1} 个: {os.path.basename(cookie_file)}")
                        else:
                            progress_callback(f"登录第 {login_count}/{need_login_count} 个账号...")
                            self.log(f"🔐 [{login_count}/{need_login_count}] 登录: {os.path.basename(cookie_file)}")

                        # 使用临时方法加载特定的cookie
                        success = self.dp_tool.load_cookies_from_parsed_data(parsed_data)
                        if not success:
                            self.log(f"❌ Cookie加载失败: {os.path.basename(cookie_file)}")
                            continue

                        # 获取账号信息（使用改进的等待方法）
                        account_info = self.dp_tool.get_account_name_and_id(fast_mode=True)

                        if account_info['name'] and account_info['id']:
                            # 为多cookie文件生成唯一的账号名称
                            if is_multi_cookie:
                                base_name = os.path.splitext(os.path.basename(cookie_file))[0]
                                account_name = f"{base_name}_#{cookie_index + 1}_{account_info['name']}"
                            else:
                                account_name = account_info['name']

                            account_record = {
                                'name': account_name,
                                'id': account_info['id'],
                                'cookies': parsed_data['cookies'],
                                'file_path': cookie_file,
                                'original_file': os.path.basename(cookie_file),
                                'source': 'login',
                                'is_multi_cookie': is_multi_cookie,
                                'cookie_index': cookie_index
                            }

                            update_callback(account_record)

                            # 对于多cookie文件，创建单独的文件保存每个账号信息
                            if is_multi_cookie:
                                self.dp_tool.save_multi_cookie_account(cookie_file, account_info, cookie_index)
                            else:
                                # 保存信息到原文件
                                self.dp_tool.save_id_to_cookie_file(cookie_file, account_info['id'], account_info['name'])

                                # 重命名文件
                                new_file_path = self.dp_tool.rename_cookie_file(cookie_file, account_info['name'])
                                if new_file_path:
                                    account_record['file_path'] = new_file_path

                        # 短暂延迟
                        time.sleep(0.2)

                    except Exception as e:
                        self.log(f"❌ 登录处理失败 {os.path.basename(file_info['file_path'])}: {e}")
                        continue

            # 更新工具中的账号数据
            self.dp_tool.accounts = self.accounts_data

            self.progress_var.set(f"完成！成功获取 {len(self.accounts_data)} 个账号")
            self.log(f"🎉 批量获取完成，共 {len(self.accounts_data)} 个账号")

            if self.accounts_data:
                messagebox.showinfo("成功", f"成功获取 {len(self.accounts_data)} 个账号信息\n其中 {len([a for a in self.accounts_data if a.get('source') == 'cached'])} 个来自缓存")
            else:
                messagebox.showwarning("警告", "没有获取到任何账号信息")

        except Exception as e:
            self.log(f"❌ 批量获取失败: {e}")
            messagebox.showerror("错误", f"批量获取失败: {e}")
        finally:
            self.dp_tool.close_browser()

    def update_account_list(self):
        """
        更新账号列表显示
        """
        # 清空现有数据
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        # 添加新数据
        for account in self.accounts_data:
            source_text = "已缓存" if account.get('source') == 'cached' else "新获取"
            self.account_tree.insert('', tk.END, values=(
                account['name'],
                account['id'],
                source_text
            ))

    def clear_accounts(self):
        """
        清空账号列表
        """
        self.accounts_data = []
        self.dp_tool.accounts = []
        self.update_account_list()
        self.progress_var.set("账号列表已清空")
        self.log("账号列表已清空")

    def start_follow(self):
        """
        开始关注操作
        """
        # 安全检测（宽松模式）
        if SECURITY_AVAILABLE:
            try:
                from advanced_security import AntiDebugger

                # 只检测最基本的Python调试器
                if AntiDebugger.check_python_debugger():
                    messagebox.showwarning("安全提示", "检测到调试环境，建议在正常环境下使用")
                    # 不直接返回，允许用户继续使用

            except Exception as e:
                self.log(f"⚠️ 安全检测异常: {str(e)}")

        if not self.accounts_data:
            messagebox.showwarning("警告", "请先获取账号信息")
            return

        try:
            # 更新线程数设置
            thread_count = int(self.thread_count_var.get())
            self.dp_tool.max_threads = min(thread_count, 20)  # 最大20个线程

            mode = self.follow_mode.get()

            # 优化的进度回调函数，减少GUI更新频率
            last_update_time = [0]  # 使用列表以便在闭包中修改

            def progress_callback(message, progress_percent=None, detail_message=None):
                current_time = time.time()
                # 限制更新频率为每0.5秒一次，避免GUI卡死
                if current_time - last_update_time[0] > 0.5:
                    def update_gui():
                        self.follow_progress_var.set(message)
                        if progress_percent is not None:
                            self.progress_bar['value'] = progress_percent
                        if detail_message:
                            self.detail_progress_var.set(detail_message)
                    self.root.after(0, update_gui)  # 使用after确保在主线程中更新
                    last_update_time[0] = current_time

            if mode == "mutual":
                # 相互关注模式
                self.log("🚀 开始相互关注...")

                # 重置进度条
                self.progress_bar['value'] = 0
                self.detail_progress_var.set("准备开始相互关注...")

                # 在新线程中执行相互关注操作，避免GUI卡死
                import threading

                def mutual_follow_worker():
                    try:
                        success, result = self.dp_tool.mutual_follow_threaded(progress_callback)

                        # 在主线程中更新UI
                        def update_ui():
                            if success:
                                self.progress_bar['value'] = 100
                                self.log(f"✅ 相互关注完成！")
                                self.follow_progress_var.set(f"相互关注完成")
                                self.detail_progress_var.set("")
                            else:
                                self.progress_bar['value'] = 0
                                self.log(f"❌ 相互关注失败: {result}")
                                self.follow_progress_var.set("相互关注失败")
                                self.detail_progress_var.set("操作失败")

                        self.root.after(0, update_ui)

                    except Exception as e:
                        def show_error():
                            self.log(f"❌ 相互关注异常: {e}")
                            self.follow_progress_var.set("相互关注异常")
                        self.root.after(0, show_error)

                mutual_follow_thread = threading.Thread(target=mutual_follow_worker, daemon=True)
                mutual_follow_thread.start()
                return  # 立即返回，不阻塞GUI

            elif mode == "batch":
                # 批量关注模式
                batch_text = self.batch_ids_text.get("1.0", "end-1c").strip()  # 修复：使用end-1c避免额外换行符

                # 检查是否是占位符文本
                placeholder_text = "请输入要关注的百家号ID，每行一个\n例如：\n1793970813566348\n1234567890123456\n9876543210987654"
                if not batch_text or batch_text == placeholder_text:
                    messagebox.showwarning("警告", "请输入要关注的ID列表")
                    return

                # 解析ID列表
                target_ids = []
                lines = batch_text.split('\n')
                self.log(f"📝 解析输入文本，共 {len(lines)} 行")

                for i, line in enumerate(lines, 1):
                    line = line.strip()
                    if line:  # 只要不是空行就处理
                        if line.isdigit() and len(line) >= 10:  # 确保是有效的ID（至少10位数字）
                            target_ids.append(line)
                            self.log(f"✅ 第{i}行：有效ID {line}")
                        else:
                            self.log(f"⚠️ 第{i}行：无效ID '{line}'（需要至少10位数字）")
                    # 空行不记录日志，避免干扰

                if not target_ids:
                    messagebox.showwarning("警告", "没有找到有效的ID\n请确保每行输入一个至少10位的数字ID")
                    return

                self.log(f"🚀 开始批量关注 {len(target_ids)} 个目标...")

                # 重置进度条
                self.progress_bar['value'] = 0
                self.detail_progress_var.set(f"准备批量关注 {len(target_ids)} 个目标...")

                # 在新线程中执行关注操作，避免GUI卡死
                import threading

                def follow_worker():
                    try:
                        success, result = self.dp_tool.batch_follow_threaded(target_ids, progress_callback)

                        # 在主线程中更新UI
                        def update_ui():
                            if success:
                                self.progress_bar['value'] = 100
                                self.log(f"✅ 批量关注完成！")
                                self.follow_progress_var.set(f"批量关注完成")
                                self.detail_progress_var.set("")
                            else:
                                self.progress_bar['value'] = 0
                                self.log(f"❌ 批量关注失败: {result}")
                                self.follow_progress_var.set("批量关注失败")
                                self.detail_progress_var.set("操作失败")

                        self.root.after(0, update_ui)

                    except Exception as e:
                        def show_error():
                            self.log(f"❌ 批量关注异常: {e}")
                            self.follow_progress_var.set("批量关注异常")
                        self.root.after(0, show_error)

                follow_thread = threading.Thread(target=follow_worker, daemon=True)
                follow_thread.start()
                return  # 立即返回，不阻塞GUI

        except Exception as e:
            self.log(f"❌ 关注操作异常: {e}")
            messagebox.showerror("错误", f"关注操作失败: {e}")

    def stop_follow(self):
        """
        停止关注操作
        """
        # 这里可以添加停止逻辑，比如设置停止标志
        self.progress_bar['value'] = 0
        self.follow_progress_var.set("用户停止操作")
        self.detail_progress_var.set("操作已停止")
        self.log("用户停止关注操作")

    def on_closing(self):
        """
        程序关闭时的处理
        """
        try:
            print("🔄 正在保存配置...")
            self.save_config()  # 保存配置
        except Exception as e:
            print(f"⚠️ 保存配置失败: {e}")

        try:
            if hasattr(self, 'dp_tool') and self.dp_tool:
                print("🔄 正在关闭浏览器...")
                self.dp_tool.close_browser()
        except Exception as e:
            print(f"⚠️ 关闭浏览器失败: {e}")
        finally:
            print("👋 程序正在退出...")
            self.root.destroy()

    def run(self):
        """
        运行GUI
        """
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"❌ GUI运行异常: {e}")
        finally:
            # 确保资源清理
            self.cleanup_resources()

    def cleanup_resources(self):
        """
        清理资源
        """
        try:
            if hasattr(self, 'dp_tool') and self.dp_tool:
                self.dp_tool.close_browser()
                self.dp_tool = None
        except Exception as e:
            print(f"⚠️ 资源清理失败: {e}")

    def __del__(self):
        """
        析构函数
        """
        self.cleanup_resources()


def main():
    """
    主函数 - 包含授权验证
    """
    print("=" * 60)
    print("百家号关注工具 v2.0")
    print("=" * 60)

    # 安全保护和授权验证
    if SECURITY_AVAILABLE:
        try:
            print("🔒 正在进行安全检测和授权验证...")

            # 初始化安全保护
            _ = initialize_security()  # 使用下划线避免未使用变量警告
            print("✅ 安全保护初始化完成")

            # 显示授权验证窗口（始终显示，提供更好的用户体验）
            print("🔑 正在显示授权验证窗口...")
            if show_license_window():
                print("✅ 授权验证成功！")
                print("🚀 启动主程序...")
            else:
                print("❌ 授权验证失败或用户取消")
                print("程序将退出...")
                sys.exit(1)

        except Exception as e:
            print(f"💥 安全检测或授权验证失败: {str(e)}")
            print("程序将退出...")
            sys.exit(1)
    else:
        print("⚠️ 安全保护模块未加载，程序将在无保护模式下运行")
        print("建议安装安全保护模块以获得更好的安全性")

        # 询问用户是否继续
        try:
            choice = input("是否继续运行程序？(y/n): ").strip().lower()
            if choice != 'y':
                print("程序退出")
                sys.exit(0)
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            sys.exit(0)

    # 启动主程序GUI
    try:
        print("🎨 正在启动用户界面...")
        app = BaiduFollowGUI()
        app.run()
    except Exception as e:
        print(f"💥 程序启动失败: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
